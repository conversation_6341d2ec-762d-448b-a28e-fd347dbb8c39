import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { DocumentService } from '@docmanager/shared';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Create HTTP server
const server = app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
});

// Graceful shutdown handler
const shutdown = () => {
  console.log('👋 Received termination signal. Starting graceful shutdown...');
  server.close(() => {
    console.log('✅ Server closed successfully');
    process.exit(0);
  });

  // Force close after 5s
  setTimeout(() => {
    console.log('⚠️ Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 5000);
};

// Listen for shutdown signals
process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);

// Initialize services
const documentService = new DocumentService();

// Initialize organization service
import { OrganizationService } from '@docmanager/shared';
const organizationService = new OrganizationService();

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Document routes
app.get('/api/documents', async (req, res) => {
  try {
    const { organizationId, projectId } = req.query;
    const documents = await documentService.getDocuments(
      organizationId as string,
      projectId as string
    );
    res.json(documents);
  } catch (error) {
    console.error('Error fetching documents:', error);
    res.status(500).json({ error: 'Failed to fetch documents' });
  }
});

app.post('/api/documents', async (req, res) => {
  try {
    // In a real implementation, you'd handle file upload here
    // For now, we'll simulate with the request body
    const { name, type, size, category, tags, organizationId, projectId } = req.body;
    
    if (!name || !type || !size || !organizationId) {
      return res.status(400).json({ error: 'Missing required fields: name, type, size, organizationId' });
    }

    const document = await documentService.createDocument({
      name,
      type,
      size,
      category: category || 'Uncategorized',
      tags: tags || [],
      organizationId,
      projectId
    });

    res.status(201).json(document);
  } catch (error) {
    console.error('Error creating document:', error);
    res.status(500).json({ error: 'Failed to create document' });
  }
});

app.delete('/api/documents/:id', async (req, res) => {
  try {
    const { id } = req.params;
    await documentService.deleteDocument(id);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting document:', error);
    res.status(500).json({ error: 'Failed to delete document' });
  }
});

// Organization routes
app.get('/api/organizations', async (req, res) => {
  try {
    const { userId } = req.query;
    const organizations = await organizationService.getOrganizations(userId as string);
    res.json(organizations);
  } catch (error) {
    console.error('Error fetching organizations:', error);
    res.status(500).json({ error: 'Failed to fetch organizations' });
  }
});

app.post('/api/organizations', async (req, res) => {
  try {
    const { name, slug, description } = req.body;
    
    if (!name || !slug) {
      return res.status(400).json({ error: 'Missing required fields: name, slug' });
    }

    const organization = await organizationService.createOrganization({
      name,
      slug,
      description
    });

    res.status(201).json(organization);
  } catch (error) {
    console.error('Error creating organization:', error);
    res.status(500).json({ error: 'Failed to create organization' });
  }
});

app.get('/api/organizations/:orgId/projects', async (req, res) => {
  try {
    const { orgId } = req.params;
    const projects = await organizationService.getProjects(orgId);
    res.json(projects);
  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).json({ error: 'Failed to fetch projects' });
  }
});

app.post('/api/organizations/:orgId/projects', async (req, res) => {
  try {
    const { orgId } = req.params;
    const { name, slug, description } = req.body;
    
    if (!name || !slug) {
      return res.status(400).json({ error: 'Missing required fields: name, slug' });
    }

    const project = await organizationService.createProject({
      name,
      slug,
      description,
      organizationId: orgId
    });

    res.status(201).json(project);
  } catch (error) {
    console.error('Error creating project:', error);
    res.status(500).json({ error: 'Failed to create project' });
  }
});

app.get('/api/context', async (req, res) => {
  try {
    const { organizationId, projectId, userId } = req.query;
    const context = await organizationService.getOrganizationContext(
      organizationId as string,
      projectId as string,
      userId as string
    );
    res.json(context);
  } catch (error) {
    console.error('Error fetching context:', error);
    res.status(500).json({ error: 'Failed to fetch context' });
  }
});

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Log server start
console.log(`📚 Environment: ${process.env.NODE_ENV || 'development'}`);