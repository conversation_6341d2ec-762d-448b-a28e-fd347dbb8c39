{"name": "@docmanager/api", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "tsx watch src/index.ts --onSignal SIGTERM", "build": "tsc", "start": "node dist/index.js", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "test": "vitest", "clean": "rm -rf dist .turbo"}, "dependencies": {"@docmanager/shared": "*", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "helmet": "^8.0.0"}, "devDependencies": {"@docmanager/config": "*", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^9.17.0", "tsx": "^4.19.2", "typescript": "^5.7.2", "vitest": "^3.2.4"}}