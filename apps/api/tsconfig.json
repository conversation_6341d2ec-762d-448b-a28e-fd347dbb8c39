{"compilerOptions": {"target": "ES2022", "lib": ["ES2023"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noUnusedLocals": true, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true}, "references": [{"path": "../../packages/shared"}], "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}