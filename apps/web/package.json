{"name": "@docmanager/web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node server.mjs", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "preview": "vite preview", "clean": "rm -rf dist .turbo"}, "dependencies": {"@tanstack/react-table": "^8.11.6", "@docmanager/shared": "*", "@docmanager/ui": "*", "lucide-react": "^0.468.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@docmanager/config": "*", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.17", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.7.2", "vite": "^6.0.5"}}