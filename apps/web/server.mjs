import { createServer } from 'vite';

async function startServer() {
  try {
    const vite = await createServer();
    await vite.listen(3001);

    vite.printUrls();

    // Graceful shutdown handler
    const shutdown = async () => {
      console.log('\n👋 Received termination signal. Starting graceful shutdown...');
      try {
        await vite.close();
        console.log('✅ Dev server closed successfully');
        process.exit(0);
      } catch (err) {
        console.error('⚠️ Error during shutdown:', err);
        process.exit(1);
      }
    };

    // Handle shutdown signals
    process.on('SIGTERM', shutdown);
    process.on('SIGINT', shutdown);

  } catch (err) {
    console.error('❌ Error starting server:', err);
    process.exit(1);
  }
}

startServer();
