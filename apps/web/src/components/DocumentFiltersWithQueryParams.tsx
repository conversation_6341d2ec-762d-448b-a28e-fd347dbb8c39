import React from 'react';
import { Search, Filter, SortAsc, SortDesc, Calendar, Grid3X3, List } from 'lucide-react';
import { useDataTableQueryParams } from '../hooks/useQueryParams';
import { DocumentFilter } from '../types/Document';

interface DocumentFiltersWithQueryParamsProps {
  categories: string[];
  totalDocuments: number;
  filteredCount: number;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  onFilterChange?: (filter: DocumentFilter) => void;
}

const DocumentFiltersWithQueryParams: React.FC<DocumentFiltersWithQueryParamsProps> = ({
  categories,
  totalDocuments,
  filteredCount,
  viewMode,
  onViewModeChange,
  onFilterChange
}) => {
  const { params, setParam, setMultipleParams, resetParams } = useDataTableQueryParams();
  
  const fileTypes = ['all', 'PDF', 'Markdown', 'DOCX', 'TXT', 'CSV'];
  const sortOptions = [
    { value: 'name', label: 'Name' },
    { value: 'uploadDate', label: 'Date' },
    { value: 'size', label: 'Size' },
    { value: 'type', label: 'Type' }
  ];





  const updateFilter = (updates: Partial<DocumentFilter>) => {
    const queryUpdates: any = {};
    
    if ('searchTerm' in updates) {
      queryUpdates.search = updates.searchTerm || null;
    }
    if ('type' in updates) {
      queryUpdates.type = updates.type === 'all' ? null : updates.type;
    }
    if ('category' in updates) {
      queryUpdates.category = updates.category === 'all' ? null : updates.category;
    }
    if ('dateRange' in updates && updates.dateRange) {
      queryUpdates.dateFrom = updates.dateRange.start;
      queryUpdates.dateTo = updates.dateRange.end;
    }
    if ('sortBy' in updates) {
      queryUpdates.sortBy = updates.sortBy;
    }
    if ('sortOrder' in updates) {
      queryUpdates.sortOrder = updates.sortOrder;
    }

    // Reset to page 1 when filters change
    queryUpdates.page = 1;
    
    setMultipleParams(queryUpdates);
  };

  const clearFilters = () => {
    resetParams();
  };

  const hasActiveFilters = 
    params.search ||
    (params.type && params.type !== 'all') ||
    (params.category && params.category !== 'all') ||
    params.dateFrom ||
    params.dateTo;

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        {/* Left side - Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search documents by name, tags, or category..."
              value={params.search || ''}
              onChange={(e) => setParam('search', e.target.value || null)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Type Filter */}
          <select
            value={params.type || 'all'}
            onChange={(e) => setParam('type', e.target.value === 'all' ? null : e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {fileTypes.map(type => (
              <option key={type} value={type}>
                {type === 'all' ? 'All Types' : type}
              </option>
            ))}
          </select>

          {/* Category Filter */}
          <select
            value={params.category || 'all'}
            onChange={(e) => setParam('category', e.target.value === 'all' ? null : e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>

          {/* Date Range */}
          <div className="flex items-center gap-2">
            <input
              type="date"
              value={params.dateFrom || ''}
              onChange={(e) => setParam('dateFrom', e.target.value || null)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <span className="text-gray-500">to</span>
            <input
              type="date"
              value={params.dateTo || ''}
              onChange={(e) => setParam('dateTo', e.target.value || null)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Right side - Sort and View Controls */}
        <div className="flex items-center gap-4">
          {/* Sort */}
          <div className="flex items-center gap-2">
            <select
              value={params.sortBy || 'uploadDate'}
              onChange={(e) => setParam('sortBy', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  Sort by {option.label}
                </option>
              ))}
            </select>
            
            <button
              onClick={() => setParam('sortOrder', params.sortOrder === 'asc' ? 'desc' : 'asc')}
              className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              title={`Sort ${params.sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
            >
              {params.sortOrder === 'asc' ? (
                <SortAsc className="h-4 w-4" />
              ) : (
                <SortDesc className="h-4 w-4" />
              )}
            </button>
          </div>

          {/* View Mode Toggle */}
          <div className="flex border border-gray-300 rounded-md overflow-hidden">
            <button
              onClick={() => onViewModeChange('list')}
              className={`p-2 ${
                viewMode === 'list'
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-white text-gray-500 hover:bg-gray-50'
              } transition-colors`}
              title="List View"
            >
              <List className="h-4 w-4" />
            </button>
            <button
              onClick={() => onViewModeChange('grid')}
              className={`p-2 ${
                viewMode === 'grid'
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-white text-gray-500 hover:bg-gray-50'
              } transition-colors`}
              title="Grid View"
            >
              <Grid3X3 className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Filter Summary and Clear */}
      <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
        <div className="text-sm text-gray-600">
          Showing {filteredCount} of {totalDocuments} documents
          {hasActiveFilters && (
            <span className="ml-2 text-blue-600">
              (filtered)
            </span>
          )}
        </div>
        
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
          >
            Clear all filters
          </button>
        )}
      </div>
    </div>
  );
};

export default DocumentFiltersWithQueryParams;
