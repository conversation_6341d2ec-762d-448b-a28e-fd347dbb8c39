import React from 'react';
import { ChevronRight, Building2, Folder<PERSON>pen, ChevronDown, Plus, Check, Settings } from 'lucide-react';
import { Organization, Project } from '@docmanager/shared';

interface BreadcrumbItem {
  label: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  isActive?: boolean;
}

interface BreadcrumbsProps {
  organization: Organization | null;
  project: Project | null;
  organizations: Organization[];
  projects: Project[];
  currentView?: string;
  currentModule?: string;
  onOrganizationChange: (orgId: string) => void;
  onProjectChange: (projectId: string | null) => void;
  onCreateOrganization?: () => void;
  onNavigate?: (target: 'organization' | 'project') => void;
  onOpenOrganizationSettings?: () => void;
  onOpenProjectSettings?: (project: Project) => void;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  organization,
  project,
  organizations,
  projects,
  currentView,
  currentModule,
  onOrganizationChange,
  onProjectChange,
  onCreateOrganization,
  onNavigate,
  onOpenOrganizationSettings,
  onOpenProjectSettings
}) => {
  const [showOrgDropdown, setShowOrgDropdown] = React.useState(false);
  const [showProjectDropdown, setShowProjectDropdown] = React.useState(false);
  const orgDropdownRef = React.useRef<HTMLDivElement>(null);
  const projectDropdownRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (orgDropdownRef.current && !orgDropdownRef.current.contains(event.target as Node)) {
        setShowOrgDropdown(false);
      }
      if (projectDropdownRef.current && !projectDropdownRef.current.contains(event.target as Node)) {
        setShowProjectDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleOrgSelect = (orgId: string) => {
    onOrganizationChange(orgId);
    setShowOrgDropdown(false);
  };

  const handleProjectSelect = (projectId: string | null) => {
    onProjectChange(projectId);
    setShowProjectDropdown(false);
  };

  const getCurrentPageLabel = () => {
    // If we're on the main dashboard or organization settings, handle specially
    if (currentModule === 'main-dashboard') {
      return null;
    }
    
    // If we're in organization settings, show "Organization Settings"
    if (currentModule === 'organization-settings') {
      return 'Organization Settings';
    }
    
    // If we're in project settings, show that
    if (currentModule === 'project-settings') {
      return 'Project Settings';
    }
    
    if (project) return project.name;
    
    const viewLabels: Record<string, string> = {
      'all-documents': 'All Documents',
      'recent': 'Recent Documents',
      'upload': 'Upload Documents',
      'search': 'Search',
      'tags': 'Tags',
      'archive': 'Archive',
      'trash': 'Trash'
    };

    return viewLabels[currentView || 'all-documents'] || 'Documents';
  };

  return (
    <nav className="flex items-center space-x-1 text-sm">
      {/* Organization Dropdown - Always show for organization settings */}
      <div className="relative" ref={orgDropdownRef}>
        <button
          onClick={() => setShowOrgDropdown(!showOrgDropdown)}
          className="flex items-center space-x-1 px-2 py-1 rounded transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100"
        >
          <Building2 className="h-4 w-4" />
          <span className="font-medium max-w-32 truncate">
            {organization?.name || 'Select Organization'}
          </span>
          <ChevronDown className="h-3 w-3" />
        </button>

        {showOrgDropdown && (
          <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
            <div className="p-2">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wider px-2 py-1 mb-1">
                Organizations
              </div>
              {organizations.map((org) => (
                <button
                  key={org.id}
                  onClick={() => handleOrgSelect(org.id)}
                  className="w-full flex items-center justify-between px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-md transition-colors"
                >
                  <div className="flex items-center space-x-2 min-w-0 flex-1">
                    <Building2 className="h-4 w-4 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <div className="truncate font-medium">{org.name}</div>
                      {org.description && (
                        <div className="truncate text-xs text-gray-500">{org.description}</div>
                      )}
                    </div>
                  </div>
                  {organization?.id === org.id && (
                    <Check className="h-4 w-4 text-blue-600" />
                  )}
                </button>
              ))}
              
              {onCreateOrganization && (
                <>
                  <div className="border-t border-gray-100 my-2" />
                  <button
                    onClick={() => {
                      onCreateOrganization();
                      setShowOrgDropdown(false);
                    }}
                    className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                  >
                    <Plus className="h-4 w-4" />
                    <span>New Organization</span>
                  </button>
                </>
              )}
              
              {/* Organization Settings */}
              <div className="border-t border-gray-100 my-2" />
              <button
                onClick={() => {
                  if (onOpenOrganizationSettings) {
                    onOpenOrganizationSettings();
                    setShowOrgDropdown(false);
                  }
                }}
                className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md transition-colors"
              >
                <Settings className="h-4 w-4" />
                <span>Organization Settings</span>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Separator - Show for organization settings or when there's a project/page */}
      {organization && (currentModule === 'organization-settings' || getCurrentPageLabel()) && (
        <ChevronRight className="h-4 w-4 text-gray-400 mx-1" />
      )}

      {/* Project Dropdown - Only show for document management, not organization settings */}
      {organization && currentModule !== 'organization-settings' && (
        <div className="relative" ref={projectDropdownRef}>
          <button
            onClick={() => setShowProjectDropdown(!showProjectDropdown)}
            className="flex items-center space-x-1 px-2 py-1 rounded transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100"
          >
            <FolderOpen className="h-4 w-4" />
            <span className="font-medium max-w-32 truncate">
              {project?.name || 'All Projects'}
            </span>
            <ChevronDown className="h-3 w-3" />
          </button>

          {showProjectDropdown && (
            <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
              <div className="p-2">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wider px-2 py-1 mb-1">
                  Projects
                </div>
                
                <button
                  onClick={() => handleProjectSelect(null)}
                  className="w-full flex items-center justify-between px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-md transition-colors"
                >
                  <div className="flex items-center space-x-2">
                    <FolderOpen className="h-4 w-4" />
                    <span className="font-medium">All Projects</span>
                  </div>
                  {!project && (
                    <Check className="h-4 w-4 text-blue-600" />
                  )}
                </button>

                {projects.map((proj) => (
                  <button
                    key={proj.id}
                    onClick={() => handleProjectSelect(proj.id)}
                    className="w-full flex items-center justify-between px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-md transition-colors"
                  >
                    <div className="flex items-center space-x-2 min-w-0 flex-1">
                      <FolderOpen className="h-4 w-4 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <div className="truncate font-medium">{proj.name}</div>
                        {proj.description && (
                          <div className="truncate text-xs text-gray-500">{proj.description}</div>
                        )}
                      </div>
                    </div>
                    {project?.id === proj.id && (
                      <Check className="h-4 w-4 text-blue-600" />
                    )}
                    
                    {/* Project Settings */}
                    {proj.id === project?.id && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onOpenProjectSettings) {
                            onOpenProjectSettings(proj);
                            setShowProjectDropdown(false);
                          }
                        }}
                        className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                        title="Project Settings"
                      >
                        <Settings className="h-3 w-3" />
                      </button>
                    )}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Separator - Only show when there's both project dropdown and current page */}
      {organization && currentModule !== 'organization-settings' && getCurrentPageLabel() && (
        <ChevronRight className="h-4 w-4 text-gray-400 mx-1" />
      )}

      {/* Current Page - Show for organization settings or document management pages */}
      {organization && getCurrentPageLabel() && (
        <div className="flex items-center space-x-1 px-2 py-1 text-blue-600">
          <span className="font-medium">
            {getCurrentPageLabel()}
          </span>
        </div>
      )}
    </nav>
  );
};

export default Breadcrumbs;