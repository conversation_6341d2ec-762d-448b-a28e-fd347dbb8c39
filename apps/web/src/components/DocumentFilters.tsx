import React from 'react';
import { Search, Filter, SortAsc, SortDesc, Calendar, Grid3X3, List } from 'lucide-react';
import { DocumentFilter } from '../types/Document';

interface DocumentFiltersProps {
  filter: DocumentFilter;
  onFilterChange: (filter: DocumentFilter) => void;
  categories: string[];
  totalDocuments: number;
  filteredCount: number;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
}

const DocumentFilters: React.FC<DocumentFiltersProps> = ({
  filter,
  onFilterChange,
  categories,
  totalDocuments,
  filteredCount,
  viewMode,
  onViewModeChange
}) => {
  const fileTypes = ['all', 'PDF', 'Markdown', 'DOCX', 'TXT', 'CSV'];
  const sortOptions = [
    { value: 'name', label: 'Name' },
    { value: 'date', label: 'Date' },
    { value: 'size', label: 'Size' },
    { value: 'type', label: 'Type' }
  ];

  const updateFilter = (updates: Partial<DocumentFilter>) => {
    onFilterChange({ ...filter, ...updates });
  };

  const clearFilters = () => {
    onFilterChange({
      searchTerm: '',
      type: 'all',
      category: 'all',
      dateRange: { start: null, end: null },
      sortBy: 'date',
      sortOrder: 'desc'
    });
  };

  const hasActiveFilters = 
    filter.searchTerm ||
    filter.type !== 'all' ||
    filter.category !== 'all' ||
    filter.dateRange.start ||
    filter.dateRange.end;

  return (
    <div className="bg-white border-b border-gray-200 p-4 space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <input
          type="text"
          placeholder="Search documents by name, tags, or category..."
          value={filter.searchTerm}
          onChange={(e) => updateFilter({ searchTerm: e.target.value })}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
        />
      </div>

      {/* Filter Row */}
      <div className="flex flex-wrap gap-4 items-center">
        {/* View Mode Toggle */}
        <div className="flex items-center border border-gray-300 rounded-md">
          <button
            onClick={() => onViewModeChange('grid')}
            className={`p-2 transition-colors ${
              viewMode === 'grid'
                ? 'bg-blue-100 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            title="Grid View"
          >
            <Grid3X3 className="h-4 w-4" />
          </button>
          <button
            onClick={() => onViewModeChange('list')}
            className={`p-2 transition-colors ${
              viewMode === 'list'
                ? 'bg-blue-100 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            title="List View"
          >
            <List className="h-4 w-4" />
          </button>
        </div>

        {/* File Type Filter */}
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <select
            value={filter.type}
            onChange={(e) => updateFilter({ type: e.target.value })}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {fileTypes.map(type => (
              <option key={type} value={type}>
                {type === 'all' ? 'All Types' : type}
              </option>
            ))}
          </select>
        </div>

        {/* Category Filter */}
        <select
          value={filter.category}
          onChange={(e) => updateFilter({ category: e.target.value })}
          className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">All Categories</option>
          {categories.map(category => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>

        {/* Date Range */}
        <div className="flex items-center space-x-2">
          <Calendar className="h-4 w-4 text-gray-500" />
          <input
            type="date"
            value={filter.dateRange.start?.toISOString().split('T')[0] || ''}
            onChange={(e) => updateFilter({
              dateRange: {
                ...filter.dateRange,
                start: e.target.value ? new Date(e.target.value) : null
              }
            })}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <span className="text-gray-500 text-sm">to</span>
          <input
            type="date"
            value={filter.dateRange.end?.toISOString().split('T')[0] || ''}
            onChange={(e) => updateFilter({
              dateRange: {
                ...filter.dateRange,
                end: e.target.value ? new Date(e.target.value) : null
              }
            })}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Sort Controls */}
        <div className="flex items-center space-x-2 ml-auto">
          <select
            value={filter.sortBy}
            onChange={(e) => updateFilter({ sortBy: e.target.value as any })}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {sortOptions.map(option => (
              <option key={option.value} value={option.value}>
                Sort by {option.label}
              </option>
            ))}
          </select>
          
          <button
            onClick={() => updateFilter({ 
              sortOrder: filter.sortOrder === 'asc' ? 'desc' : 'asc' 
            })}
            className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
            title={`Sort ${filter.sortOrder === 'asc' ? 'descending' : 'ascending'}`}
          >
            {filter.sortOrder === 'asc' ? 
              <SortAsc className="h-4 w-4" /> : 
              <SortDesc className="h-4 w-4" />
            }
          </button>
        </div>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
          >
            Clear Filters
          </button>
        )}
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-gray-600">
        <span>
          Showing {filteredCount} of {totalDocuments} documents
        </span>
        {hasActiveFilters && (
          <span className="text-blue-600">
            {totalDocuments - filteredCount} documents filtered out
          </span>
        )}
      </div>
    </div>
  );
};

export default DocumentFilters;