import React, { useState, useRef } from 'react';
import { Upload, X, File, CheckCircle, AlertCircle } from 'lucide-react';
import { UploadProgress } from '../types/Document';

interface DocumentUploadProps {
  onUpload: (files: File[]) => void;
  accepts?: string[];
  maxSize?: number; // in bytes
  multiple?: boolean;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({
  onUpload,
  accepts = ['.pdf', '.md', '.docx', '.txt', '.csv'],
  maxSize = 10 * 1024 * 1024, // 10MB
  multiple = true
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
  const [errors, setErrors] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    handleFiles(files);
  };

  const handleFiles = (files: File[]) => {
    const validFiles: File[] = [];
    const newErrors: string[] = [];

    files.forEach(file => {
      // Check file type
      const extension = '.' + file.name.split('.').pop()?.toLowerCase();
      if (!accepts.includes(extension)) {
        newErrors.push(`${file.name}: Unsupported file type. Allowed: ${accepts.join(', ')}`);
        return;
      }

      // Check file size
      if (file.size > maxSize) {
        newErrors.push(`${file.name}: File too large. Max size: ${formatFileSize(maxSize)}`);
        return;
      }

      validFiles.push(file);
    });

    setErrors(newErrors);

    if (validFiles.length > 0) {
      // Simulate upload progress
      const progressItems: UploadProgress[] = validFiles.map(file => ({
        id: Date.now().toString() + Math.random(),
        name: file.name,
        progress: 0,
        status: 'uploading'
      }));

      setUploadProgress(progressItems);

      // Simulate progress updates
      progressItems.forEach((item, index) => {
        const interval = setInterval(() => {
          setUploadProgress(prev => 
            prev.map(p => {
              if (p.id === item.id) {
                const newProgress = Math.min(p.progress + Math.random() * 30, 100);
                const newStatus = newProgress >= 100 ? 'completed' : 'uploading';
                
                if (newStatus === 'completed') {
                  clearInterval(interval);
                  // Remove from progress after a delay
                  setTimeout(() => {
                    setUploadProgress(prev => prev.filter(p => p.id !== item.id));
                  }, 2000);
                }
                
                return { ...p, progress: newProgress, status: newStatus };
              }
              return p;
            })
          );
        }, 200);
      });

      onUpload(validFiles);
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const removeError = (index: number) => {
    setErrors(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
          isDragOver
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={accepts.join(',')}
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Upload Documents
        </h3>
        <p className="text-sm text-gray-500 mb-4">
          Drag and drop files here, or click to select files
        </p>
        <p className="text-xs text-gray-400">
          Supported: {accepts.join(', ')} • Max size: {formatFileSize(maxSize)}
        </p>
      </div>

      {/* Upload Progress */}
      {uploadProgress.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-900">Uploading Files</h4>
          {uploadProgress.map(item => (
            <div key={item.id} className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <File className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-900 truncate">
                    {item.name}
                  </span>
                </div>
                {item.status === 'completed' && (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                )}
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    item.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${item.progress}%` }}
                />
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {Math.round(item.progress)}% • {item.status}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Errors */}
      {errors.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-red-900">Upload Errors</h4>
          {errors.map((error, index) => (
            <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-red-800">{error}</span>
                </div>
                <button
                  onClick={() => removeError(index)}
                  className="text-red-400 hover:text-red-600 transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DocumentUpload;