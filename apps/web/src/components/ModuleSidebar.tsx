import React from 'react';
import { 
  FileText, 
  Home,
  Settings,
  ChevronRight
} from 'lucide-react';
import { AppModule, ModuleConfig } from '../types/AppState';

interface ModuleSidebarProps {
  isCollapsed: boolean;
  currentModule: AppModule;
  onModuleSelect: (module: AppModule) => void;
  onOpenOrganizationSettings?: () => void;
}

const ModuleSidebar: React.FC<ModuleSidebarProps> = ({
  isCollapsed,
  currentModule,
  onModuleSelect,
  onOpenOrganizationSettings
}) => {
  const modules: ModuleConfig[] = [
    {
      id: 'main-dashboard',
      name: 'Dashboard',
      description: 'Overview and analytics',
      icon: <Home className="h-5 w-5" />,
      path: '/dashboard'
    },
    {
      id: 'document-management',
      name: 'Document Management',
      description: 'Manage and organize documents',
      icon: <FileText className="h-5 w-5" />,
      path: '/documents'
    }
  ];

  const renderModuleItem = (module: ModuleConfig) => {
    const isActive = currentModule === module.id;

    return (
      <button
        key={module.id}
        className={`flex items-center w-full px-3 py-3 text-left text-sm rounded-md transition-all duration-200 cursor-pointer group ${
          isActive
            ? 'bg-blue-100 text-blue-700 shadow-sm'
            : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
        }`}
        onClick={() => onModuleSelect(module.id)}
      >
        <div className="flex items-center min-w-0 flex-1">
          <span className={`flex-shrink-0 transition-colors ${
            isActive ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'
          }`}>
            {module.icon}
          </span>
          
          {!isCollapsed && (
            <div className="ml-3 min-w-0 flex-1">
              <div className="font-medium truncate">{module.name}</div>
              <div className={`text-xs mt-0.5 truncate ${
                isActive ? 'text-blue-600' : 'text-gray-500'
              }`}>
                {module.description}
              </div>
            </div>
          )}
        </div>
        
        {!isCollapsed && (
          <ChevronRight className={`h-4 w-4 transition-colors ${
            isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-600'
          }`} />
        )}
      </button>
    );
  };

  const isSettingsActive = currentModule === 'organization-settings';

  return (
    <div className={`flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    }`}>
      {/* Logo/Brand */}
      <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200">
        {isCollapsed ? (
          <FileText className="h-8 w-8 text-blue-600" />
        ) : (
          <div className="flex items-center space-x-2">
            <FileText className="h-8 w-8 text-blue-600" />
            <span className="text-xl font-bold text-gray-900">Anter</span>
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 py-6 space-y-2">
        <div className={`${isCollapsed ? 'hidden' : 'block'} mb-4`}>
          <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
            Modules
          </h3>
        </div>
        
        {modules.map(module => renderModuleItem(module))}
      </nav>

      {/* Settings */}
      <div className="p-2 border-t border-gray-200">
        <button
          onClick={onOpenOrganizationSettings}
          className={`flex items-center w-full px-3 py-3 text-left text-sm rounded-md transition-colors cursor-pointer ${
            isSettingsActive
              ? 'bg-blue-100 text-blue-700'
              : 'text-gray-700 hover:bg-gray-100'
          }`}
        >
          <Settings className={`h-5 w-5 ${
            isSettingsActive ? 'text-blue-600' : 'text-gray-500'
          }`} />
          {!isCollapsed && (
            <span className="ml-3">Settings</span>
          )}
        </button>
      </div>

      {/* Storage indicator for main dashboard */}
      {!isCollapsed && currentModule === 'document-management' && (
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 mb-2">Storage Used</div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-blue-500 h-2 rounded-full" style={{ width: '45%' }} />
          </div>
          <div className="text-xs text-gray-500 mt-1">2.1 GB of 5 GB used</div>
        </div>
      )}
    </div>
  );
};

export default ModuleSidebar;