import React, { useState, useRef, useEffect } from 'react';
import { 
  ChevronDown, 
  Building2, 
  FolderOpen, 
  Plus,
  Check
} from 'lucide-react';
import { Organization, Project } from '@docmanager/shared';

interface OrganizationSelectorProps {
  currentOrganization: Organization | null;
  currentProject: Project | null;
  organizations: Organization[];
  projects: Project[];
  onOrganizationChange: (orgId: string) => void;
  onProjectChange: (projectId: string | null) => void;
  onCreateOrganization?: () => void;
  onCreateProject?: () => void;
}

const OrganizationSelector: React.FC<OrganizationSelectorProps> = ({
  currentOrganization,
  currentProject,
  organizations,
  projects,
  onOrganizationChange,
  onProjectChange,
  onCreateOrganization,
  onCreateProject
}) => {
  const [showOrgDropdown, setShowOrgDropdown] = useState(false);
  const [showProjectDropdown, setShowProjectDropdown] = useState(false);
  const orgDropdownRef = useRef<HTMLDivElement>(null);
  const projectDropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (orgDropdownRef.current && !orgDropdownRef.current.contains(event.target as Node)) {
        setShowOrgDropdown(false);
      }
      if (projectDropdownRef.current && !projectDropdownRef.current.contains(event.target as Node)) {
        setShowProjectDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleOrgSelect = (orgId: string) => {
    onOrganizationChange(orgId);
    setShowOrgDropdown(false);
    // Reset project when organization changes
    onProjectChange(null);
  };

  const handleProjectSelect = (projectId: string | null) => {
    onProjectChange(projectId);
    setShowProjectDropdown(false);
  };

  return (
    <div className="flex items-center space-x-3">
      {/* Organization Selector */}
      <div className="relative" ref={orgDropdownRef}>
        <button
          onClick={() => setShowOrgDropdown(!showOrgDropdown)}
          className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
        >
          <Building2 className="h-4 w-4 text-gray-500" />
          <span className="max-w-32 truncate">
            {currentOrganization?.name || 'Select Organization'}
          </span>
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </button>

        {showOrgDropdown && (
          <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
            <div className="p-2">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wider px-2 py-1 mb-1">
                Organizations
              </div>
              {organizations.map((org) => (
                <button
                  key={org.id}
                  onClick={() => handleOrgSelect(org.id)}
                  className="w-full flex items-center justify-between px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-md transition-colors"
                >
                  <div className="flex items-center space-x-2 min-w-0 flex-1">
                    <Building2 className="h-4 w-4 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <div className="truncate font-medium">{org.name}</div>
                      {org.description && (
                        <div className="truncate text-xs text-gray-500">{org.description}</div>
                      )}
                    </div>
                  </div>
                  {currentOrganization?.id === org.id && (
                    <Check className="h-4 w-4 text-blue-600" />
                  )}
                </button>
              ))}
              
              {onCreateOrganization && (
                <>
                  <div className="border-t border-gray-100 my-2" />
                  <button
                    onClick={() => {
                      onCreateOrganization();
                      setShowOrgDropdown(false);
                    }}
                    className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                  >
                    <Plus className="h-4 w-4" />
                    <span>New Organization</span>
                  </button>
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Project Selector */}
      {currentOrganization && (
        <div className="relative" ref={projectDropdownRef}>
          <button
            onClick={() => setShowProjectDropdown(!showProjectDropdown)}
            className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
          >
            <FolderOpen className="h-4 w-4 text-gray-500" />
            <span className="max-w-32 truncate">
              {currentProject?.name || 'All Projects'}
            </span>
            <ChevronDown className="h-4 w-4 text-gray-400" />
          </button>

          {showProjectDropdown && (
            <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
              <div className="p-2">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wider px-2 py-1 mb-1">
                  Projects
                </div>
                
                <button
                  onClick={() => handleProjectSelect(null)}
                  className="w-full flex items-center justify-between px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-md transition-colors"
                >
                  <div className="flex items-center space-x-2">
                    <FolderOpen className="h-4 w-4" />
                    <span className="font-medium">All Projects</span>
                  </div>
                  {!currentProject && (
                    <Check className="h-4 w-4 text-blue-600" />
                  )}
                </button>

                {projects.map((project) => (
                  <button
                    key={project.id}
                    onClick={() => handleProjectSelect(project.id)}
                    className="w-full flex items-center justify-between px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-md transition-colors"
                  >
                    <div className="flex items-center space-x-2 min-w-0 flex-1">
                      <FolderOpen className="h-4 w-4 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <div className="truncate font-medium">{project.name}</div>
                        {project.description && (
                          <div className="truncate text-xs text-gray-500">{project.description}</div>
                        )}
                      </div>
                    </div>
                    {currentProject?.id === project.id && (
                      <Check className="h-4 w-4 text-blue-600" />
                    )}
                  </button>
                ))}
                
                {onCreateProject && (
                  <>
                    <div className="border-t border-gray-100 my-2" />
                    <button
                      onClick={() => {
                        onCreateProject();
                        setShowProjectDropdown(false);
                      }}
                      className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                    >
                      <Plus className="h-4 w-4" />
                      <span>New Project</span>
                    </button>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default OrganizationSelector;