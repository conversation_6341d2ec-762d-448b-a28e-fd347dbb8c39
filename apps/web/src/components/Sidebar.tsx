import React, { useState } from 'react';
import { 
  FileText, 
  Upload, 
  Folder, 
  Archive,
  Trash2,
  ChevronDown,
  ChevronRight,
  Search,
  Tag,
  Clock,
  ArrowLeft
} from 'lucide-react';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  count?: number;
  children?: SidebarItem[];
}

interface SidebarProps {
  isCollapsed: boolean;
  activeItem: string;
  onItemClick: (id: string) => void;
  onBackToDashboard?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, activeItem, onItemClick, onBackToDashboard }) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const sidebarItems: SidebarItem[] = [
    {
      id: 'documents',
      label: 'Documents',
      icon: <FileText className="h-5 w-5" />,
      count: 6,
      children: [
        {
          id: 'all-documents',
          label: 'All Documents',
          icon: <FileText className="h-4 w-4" />,
          count: 6
        },
        {
          id: 'recent',
          label: 'Recent',
          icon: <Clock className="h-4 w-4" />,
          count: 3
        }
      ]
    },
    {
      id: 'categories',
      label: 'Categories',
      icon: <Folder className="h-5 w-5" />,
      children: [
        {
          id: 'projects',
          label: 'Projects',
          icon: <Folder className="h-4 w-4" />,
          count: 1
        },
        {
          id: 'meetings',
          label: 'Meetings',
          icon: <Folder className="h-4 w-4" />,
          count: 1
        },
        {
          id: 'finance',
          label: 'Finance',
          icon: <Folder className="h-4 w-4" />,
          count: 1
        },
        {
          id: 'research',
          label: 'Research',
          icon: <Folder className="h-4 w-4" />,
          count: 1
        },
        {
          id: 'technical',
          label: 'Technical',
          icon: <Folder className="h-4 w-4" />,
          count: 2
        }
      ]
    },
    {
      id: 'upload',
      label: 'Upload',
      icon: <Upload className="h-5 w-5" />
    },
    {
      id: 'search',
      label: 'Search',
      icon: <Search className="h-5 w-5" />
    },
    {
      id: 'tags',
      label: 'Tags',
      icon: <Tag className="h-5 w-5" />
    },
    {
      id: 'archive',
      label: 'Archive',
      icon: <Archive className="h-5 w-5" />
    },
    {
      id: 'trash',
      label: 'Trash',
      icon: <Trash2 className="h-5 w-5" />
    }
  ];

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const renderSidebarItem = (item: SidebarItem, level: number = 0) => {
    const isActive = activeItem === item.id;
    const isExpanded = expandedItems.has(item.id);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.id}>
        <div
          className={`flex items-center w-full px-3 py-2 text-left text-sm rounded-md transition-colors group cursor-pointer ${
            isActive
              ? 'bg-blue-100 text-blue-700'
              : 'text-gray-700 hover:bg-gray-100'
          } ${level > 0 ? 'ml-4' : ''}`}
          onClick={() => {
            if (hasChildren && !isCollapsed) {
              toggleExpanded(item.id);
            } else {
              onItemClick(item.id);
            }
          }}
          style={{ paddingLeft: isCollapsed ? '12px' : `${12 + level * 16}px` }}
        >
          <div className="flex items-center min-w-0 flex-1">
            <span className="flex-shrink-0">
              {item.icon}
            </span>
            
            {!isCollapsed && (
              <>
                <span className="ml-3 truncate">{item.label}</span>
                {item.count !== undefined && (
                  <span className={`ml-auto text-xs px-2 py-1 rounded-full ${
                    isActive ? 'bg-blue-200 text-blue-800' : 'bg-gray-200 text-gray-600'
                  }`}>
                    {item.count}
                  </span>
                )}
              </>
            )}
          </div>
          
          {hasChildren && !isCollapsed && (
            <span className="flex-shrink-0 ml-2">
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </span>
          )}
        </div>

        {/* Render children */}
        {hasChildren && isExpanded && !isCollapsed && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderSidebarItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300 ${
      isCollapsed ? 'w-16 mr-4' : 'w-64'
    }`}>
      {/* Logo/Brand */}
      <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200">
        {isCollapsed ? (
          <FileText className="h-8 w-8 text-blue-600" />
        ) : (
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center space-x-2">
            <FileText className="h-8 w-8 text-blue-600" />
            <span className="text-xl font-bold text-gray-900">Anter</span>
            </div>
            {onBackToDashboard && (
              <button
                onClick={onBackToDashboard}
                className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                title="Back to Dashboard"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
            )}
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
        {!isCollapsed && (
          <div className="mb-4">
            <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
              Document Management
            </h3>
          </div>
        )}
        {sidebarItems.map(item => renderSidebarItem(item))}
      </nav>

      {/* Bottom Section */}
      {!isCollapsed && (
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 mb-2">Storage Used</div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-blue-500 h-2 rounded-full" style={{ width: '45%' }} />
          </div>
          <div className="text-xs text-gray-500 mt-1">2.1 GB of 5 GB used</div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;