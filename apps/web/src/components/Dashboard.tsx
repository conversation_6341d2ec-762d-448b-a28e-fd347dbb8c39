import React, { useState, useEffect } from 'react';
import { 
  Menu, 
  Bell, 
  User, 
  Plus,
  TrendingUp,
  FileText,
  Upload as UploadIcon,
  HardDrive
} from 'lucide-react';
import Sidebar from './Sidebar';
import ModuleSidebar from './ModuleSidebar';
import MobileMenu from './MobileMenu';
import DocumentList from './DocumentList';
import DocumentGrid from './DocumentGrid';
import DocumentUpload from './DocumentUpload';
import DocumentFilters from './DocumentFilters';
import DocumentFiltersWithQueryParams from './DocumentFiltersWithQueryParams';
import { useDataTableQueryParams } from '../hooks/useQueryParams';
import ConfirmDialog from './ConfirmDialog';
import OrganizationSelector from './OrganizationSelector';
import Breadcrumbs from './Breadcrumbs';
import CreateOrganizationModal from './CreateOrganizationModal';
import OrganizationSettings from './OrganizationSettings';
import ProjectSettings from './ProjectSettings';
import { Document, DocumentFilter } from '../types/Document';
import { OrganizationService, Organization, Project } from '@docmanager/shared';
import { DocumentService } from '../services/DocumentService';

const documentService = DocumentService.getInstance();
const organizationService = new OrganizationService();
import { useAppState } from '../hooks/useAppState';

const Dashboard: React.FC = () => {
  const { appState, setCurrentModule, updateDocumentManagement, updateOrganization, updateSettings } = useAppState();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const { params: queryParams } = useDataTableQueryParams();
  const [loading, setLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null);
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [showCreateOrgModal, setShowCreateOrgModal] = useState(false);

  // Add settings-related state
  const [selectedProjectForSettings, setSelectedProjectForSettings] = useState<Project | null>(null);

  const [filter, setFilter] = useState<DocumentFilter>({
    searchTerm: '',
    type: 'all',
    category: 'all',
    dateRange: { start: null, end: null },
    sortBy: 'date',
    sortOrder: 'desc'
  });

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (appState.organization.currentOrganizationId) {
      loadDocuments();
    }
  }, [appState.organization.currentOrganizationId, appState.organization.currentProjectId]);

  useEffect(() => {
    if (appState.currentModule === 'documentManagement') {
      // Use query parameters for filtering in document management
      const queryFilter: DocumentFilter = {
        searchTerm: queryParams.search || '',
        type: queryParams.type || 'all',
        category: queryParams.category || 'all',
        dateRange: {
          start: queryParams.dateFrom || null,
          end: queryParams.dateTo || null
        },
        sortBy: queryParams.sortBy || 'uploadDate',
        sortOrder: queryParams.sortOrder || 'desc'
      };
      const filtered = documentService.filterDocuments(documents, queryFilter);
      setFilteredDocuments(filtered);
    } else {
      // Use regular filter for dashboard overview
      const filtered = documentService.filterDocuments(documents, filter);
      setFilteredDocuments(filtered);
    }
  }, [documents, filter, queryParams, appState.currentModule]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Load organizations
      const orgs = await organizationService.getOrganizations();
      setOrganizations(orgs);
      
      // Set default organization if none selected
      if (!appState.organization.currentOrganizationId && orgs.length > 0) {
        const defaultOrg = orgs[0];
        updateOrganization({ 
          currentOrganizationId: defaultOrg.id,
          availableOrganizations: orgs
        });
        setCurrentOrganization(defaultOrg);
        
        // Load projects for default organization
        const projects = await organizationService.getProjects(defaultOrg.id);
        setProjects(projects);
        updateOrganization({ availableProjects: projects });
      } else if (appState.organization.currentOrganizationId) {
        const org = orgs.find(o => o.id === appState.organization.currentOrganizationId);
        setCurrentOrganization(org || null);
        
        if (org) {
          const projects = await organizationService.getProjects(org.id);
          setProjects(projects);
          updateOrganization({ availableProjects: projects });
        }
      }
      
      updateOrganization({ availableOrganizations: orgs });
    } catch (error) {
      console.error('Failed to load initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDocuments = async () => {
    try {
      const docs = await documentService.getDocuments(
        appState.organization.currentOrganizationId || undefined,
        appState.organization.currentProjectId || undefined
      );
      setDocuments(docs);
    } catch (error) {
      console.error('Failed to load documents:', error);
    }
  };

  const handleUpload = async (files: File[]) => {
    if (!appState.organization.currentOrganizationId) {
      console.error('No organization selected');
      return;
    }

    try {
      for (const file of files) {
        const newDoc = await documentService.createDocument({
          name: file.name,
          type: getFileType(file.name),
          size: file.size,
          category: 'Uncategorized',
          tags: [],
          organizationId: appState.organization.currentOrganizationId,
          projectId: appState.organization.currentProjectId || undefined
        });
        setDocuments(prev => [newDoc, ...prev]);
      }
    } catch (error) {
      console.error('Failed to upload document:', error);
    }
  };

  const getFileType = (filename: string): Document['type'] => {
    const extension = filename.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf': return 'PDF';
      case 'doc':
      case 'docx': return 'DOCX';
      case 'txt': return 'TXT';
      case 'md': return 'Markdown';
      case 'csv': return 'CSV';
      default: return 'PDF';
    }
  };

  const handleDeleteDocument = (id: string) => {
    setDocumentToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (documentToDelete) {
      try {
        await documentService.deleteDocument(documentToDelete);
        setDocuments(prev => prev.filter(doc => doc.id !== documentToDelete));
        setDeleteDialogOpen(false);
        setDocumentToDelete(null);
      } catch (error) {
        console.error('Failed to delete document:', error);
      }
    }
  };

  const handleSidebarItemClick = (id: string) => {
    updateDocumentManagement({ activeView: id });
    if (id === 'upload') {
      updateDocumentManagement({ showUpload: true });
    } else if (id === 'settings') {
      setCurrentModule('organization-settings');
      updateDocumentManagement({ showUpload: false });
    } else {
      updateDocumentManagement({ showUpload: false });
    }
  };

  const handleModuleSelect = (module: any) => {
    setCurrentModule(module);
    if (module === 'document-management') {
      updateDocumentManagement({ activeView: 'all-documents', showUpload: false });
    } else if (module === 'organization-settings') {
      updateDocumentManagement({ showUpload: false });
    } else if (module === 'project-settings') {
      updateDocumentManagement({ showUpload: false });
    }
  };

  const handleBackToDashboard = () => {
    setCurrentModule('main-dashboard');
  };

  const handleOrganizationChange = async (orgId: string) => {
    const org = organizations.find(o => o.id === orgId);
    if (org) {
      setCurrentOrganization(org);
      setCurrentProject(null);
      updateOrganization({ 
        currentOrganizationId: orgId,
        currentProjectId: null
      });
      
      // Load projects for the new organization
      const projects = await organizationService.getProjects(orgId);
      setProjects(projects);
      updateOrganization({ availableProjects: projects });
    }
  };

  const handleProjectChange = (projectId: string | null) => {
    const project = projectId ? projects.find(p => p.id === projectId) : null;
    setCurrentProject(project);
    updateOrganization({ currentProjectId: projectId });
  };

  const handleCreateOrganization = async (data: { name: string; slug: string; description?: string }) => {
    try {
      const newOrg = await organizationService.createOrganization(data);
      const updatedOrgs = [...organizations, newOrg];
      setOrganizations(updatedOrgs);
      updateOrganization({ availableOrganizations: updatedOrgs });
      
      // Switch to the new organization
      setCurrentOrganization(newOrg);
      updateOrganization({ 
        currentOrganizationId: newOrg.id,
        currentProjectId: null
      });
      
      // Load projects (will be empty for new org)
      setProjects([]);
      updateOrganization({ availableProjects: [] });
    } catch (error) {
      console.error('Failed to create organization:', error);
      throw error;
    }
  };

  const handleOpenOrganizationSettings = () => {
    setCurrentModule('organization-settings');
  };

  const handleOpenProjectSettings = (project: Project) => {
    setSelectedProjectForSettings(project);
    setCurrentModule('project-settings');
  };

  const handleUpdateOrganization = async (updates: Partial<Organization>) => {
    try {
      // In real implementation, this would call the API
      const updatedOrgs = organizations.map(org => 
        org.id === currentOrganization?.id ? { ...org, ...updates } : org
      );
      setOrganizations(updatedOrgs);
      if (currentOrganization) {
        setCurrentOrganization({ ...currentOrganization, ...updates });
      }
    } catch (error) {
      console.error('Failed to update organization:', error);
    }
  };

  const handleDeleteOrganization = async (orgId: string) => {
    try {
      // In real implementation, this would call the API
      const updatedOrgs = organizations.filter(org => org.id !== orgId);
      setOrganizations(updatedOrgs);
      updateOrganization({ availableOrganizations: updatedOrgs });
      
      // Switch to first available organization or reset
      if (updatedOrgs.length > 0) {
        handleOrganizationChange(updatedOrgs[0].id);
      } else {
        setCurrentOrganization(null);
        updateOrganization({ currentOrganizationId: null });
      }
      
      // Go back to dashboard
      setCurrentModule('main-dashboard');
    } catch (error) {
      console.error('Failed to delete organization:', error);
    }
  };

  const handleUpdateProject = async (updates: Partial<Project>) => {
    try {
      // In real implementation, this would call the API
      const updatedProjects = projects.map(proj => 
        proj.id === selectedProjectForSettings?.id ? { ...proj, ...updates } : proj
      );
      setProjects(updatedProjects);
      updateOrganization({ availableProjects: updatedProjects });
      
      if (selectedProjectForSettings) {
        setSelectedProjectForSettings({ ...selectedProjectForSettings, ...updates });
      }
      if (currentProject?.id === selectedProjectForSettings?.id) {
        setCurrentProject({ ...currentProject, ...updates });
      }
    } catch (error) {
      console.error('Failed to update project:', error);
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    try {
      // In real implementation, this would call the API
      const updatedProjects = projects.filter(proj => proj.id !== projectId);
      setProjects(updatedProjects);
      updateOrganization({ availableProjects: updatedProjects });
      
      // Reset current project if it was deleted
      if (currentProject?.id === projectId) {
        handleProjectChange(null);
      }
      
      // Go back to dashboard
      setCurrentModule('main-dashboard');
    } catch (error) {
      console.error('Failed to delete project:', error);
    }
  };

  const getViewTitle = () => {
    if (appState.currentModule === 'main-dashboard') {
      return 'Dashboard Overview';
    }
    
    if (appState.currentModule === 'organization-settings') {
      return 'Organization Settings';
    }
    
    if (appState.currentModule === 'project-settings') {
      return 'Project Settings';
    }
    
    if (currentProject) {
      return `${currentProject.name} Documents`;
    }
    
    switch (appState.documentManagement.activeView) {
      case 'all-documents':
        return `${currentOrganization?.name || 'Organization'} Documents`;
      case 'recent':
        return 'Recent Documents';
      case 'upload':
        return 'Upload Documents';
      default:
        return `${currentOrganization?.name || 'Organization'} Documents`;
    }
  };

  const getPageContent = () => {
    if (appState.currentModule === 'organization-settings' && currentOrganization) {
      return (
        <OrganizationSettings
          organization={currentOrganization}
          onBack={() => setCurrentModule('main-dashboard')}
          onUpdate={handleUpdateOrganization}
          onDelete={handleDeleteOrganization}
        />
      );
    }

    if (appState.currentModule === 'project-settings' && selectedProjectForSettings) {
      return (
        <ProjectSettings
          project={selectedProjectForSettings}
          onBack={() => setCurrentModule('main-dashboard')}
          onUpdate={handleUpdateProject}
          onDelete={handleDeleteProject}
        />
      );
    }

    if (appState.documentManagement.showUpload || appState.documentManagement.activeView === 'upload') {
      return (
        <div className="max-w-4xl">
          <DocumentUpload onUpload={handleUpload} />
        </div>
      );
    }

    if (appState.currentModule === 'main-dashboard') {
      return (
        <div className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Documents</p>
                  <p className="text-2xl font-semibold text-gray-900">{documents.length}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center">
                <UploadIcon className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">This Month</p>
                  <p className="text-2xl font-semibold text-gray-900">12</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center">
                <HardDrive className="h-8 w-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Storage Used</p>
                  <p className="text-2xl font-semibold text-gray-900">2.1 GB</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-orange-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Growth</p>
                  <p className="text-2xl font-semibold text-gray-900">+23%</p>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Documents */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Recent Documents</h3>
            </div>
            <div className="p-6">
              <DocumentList
                documents={filteredDocuments.slice(0, 6)}
                pageSize={3}
                onDelete={handleDeleteDocument}
                tableId="recent-documents-table"
                sidebarCollapsed={sidebarCollapsed}
              />
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <DocumentFiltersWithQueryParams
          categories={documentService.getCategories()}
          totalDocuments={documents.length}
          filteredCount={filteredDocuments.length}
          viewMode={appState.documentManagement.viewMode}
          onViewModeChange={(mode) => updateDocumentManagement({ viewMode: mode })}
        />
        
        {appState.documentManagement.viewMode === 'grid' ? (
          <DocumentGrid
            documents={filteredDocuments}
            onDelete={handleDeleteDocument}
          />
        ) : (
          <DocumentList
            documents={filteredDocuments}
            tableId="main-documents-table"
            sidebarCollapsed={sidebarCollapsed}
            enableQueryParams={true}
          />
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading documents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Desktop Sidebar */}
      <div className="hidden lg:block">
        {appState.currentModule === 'main-dashboard' || appState.currentModule === 'organization-settings' ? (
          <ModuleSidebar
            isCollapsed={sidebarCollapsed}
            currentModule={appState.currentModule}
            onModuleSelect={handleModuleSelect}
            onOpenOrganizationSettings={handleOpenOrganizationSettings}
          />
        ) : (
          <Sidebar
            isCollapsed={sidebarCollapsed}
            activeItem={appState.documentManagement.activeView}
            onItemClick={handleSidebarItemClick}
            onBackToDashboard={handleBackToDashboard}
          />
        )}
      </div>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={mobileMenuOpen}
        onClose={() => setMobileMenuOpen(false)}
        activeItem={appState.documentManagement.activeView}
        onItemClick={handleSidebarItemClick}
        currentModule={appState.currentModule}
        onModuleSelect={handleModuleSelect}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Main Header */}
        <header className="bg-white border-b border-gray-200 h-16 sticky top-0 z-40">
          <div className="flex items-center justify-between px-0 h-full">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setMobileMenuOpen(true)}
                className="lg:hidden p-2 text-gray-500 hover:text-gray-700 transition-colors"
              >
                <Menu className="h-6 w-6" />
              </button>
              
              <button
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                className="hidden lg:block p-2 text-gray-500 hover:text-gray-700 transition-colors"
              >
                <Menu className="h-6 w-6" />
              </button>
              
              {/* Enhanced Breadcrumbs with integrated dropdowns */}
              <div className="hidden md:block">
                <Breadcrumbs
                  organization={currentOrganization}
                  project={currentProject}
                  currentView={appState.documentManagement.activeView}
                  organizations={organizations}
                  projects={projects}
                  onOrganizationChange={handleOrganizationChange}
                  onProjectChange={handleProjectChange}
                  onCreateOrganization={() => setShowCreateOrgModal(true)}
                  currentModule={appState.currentModule}
                  onOpenOrganizationSettings={handleOpenOrganizationSettings}
                  onOpenProjectSettings={handleOpenProjectSettings}
                  onNavigate={(target) => {
                    switch (target) {
                      case 'organization':
                        setCurrentModule('document-management');
                        updateDocumentManagement({ activeView: 'all-documents' });
                        handleProjectChange(null);
                        break;
                      case 'project':
                        setCurrentModule('document-management');
                        updateDocumentManagement({ activeView: 'all-documents' });
                        break;
                    }
                  }}
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {appState.documentManagement.activeView !== 'upload' && !appState.documentManagement.showUpload && appState.currentModule === 'document-management' && (
                <button
                  onClick={() => updateDocumentManagement({ showUpload: true })}
                  className="hidden sm:inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Upload
                </button>
              )}
              
              <button className="p-2 text-gray-500 hover:text-gray-700 transition-colors">
                <Bell className="h-6 w-6" />
              </button>
              
              <button className="pr-4 text-gray-500 hover:text-gray-700 transition-colors">
                <User className="h-6 w-6" />
              </button>
            </div>
          </div>
        </header>

        {/* Page Header with Title Only */}
        <div className="bg-gray-50 border-b border-gray-200 py-4 sticky top-16 z-30 px-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">
              {getViewTitle()}
            </h1>
            
            {/* Mobile breadcrumbs with dropdowns - shown only on small screens */}
            <div className="md:hidden">
              <Breadcrumbs
                organization={currentOrganization}
                project={currentProject}
                currentView={appState.documentManagement.activeView}
                organizations={organizations}
                projects={projects}
                onOrganizationChange={handleOrganizationChange}
                onProjectChange={handleProjectChange}
                onCreateOrganization={() => setShowCreateOrgModal(true)}
                currentModule={appState.currentModule}
                onOpenOrganizationSettings={handleOpenOrganizationSettings}
                onOpenProjectSettings={handleOpenProjectSettings}
                onNavigate={(target) => {
                  switch (target) {
                    case 'organization':
                      setCurrentModule('document-management');
                      updateDocumentManagement({ activeView: 'all-documents' });
                      handleProjectChange(null);
                      break;
                    case 'project':
                      setCurrentModule('document-management');
                      updateDocumentManagement({ activeView: 'all-documents' });
                      break;
                  }
                }}
              />
            </div>
          </div>
        </div>

        {/* Page Content */}
        <main className="flex-1 overflow-auto p-6 pt-0">
          <div className="py-6">
            {getPageContent()}
          </div>
        </main>
      </div>
      <ConfirmDialog
        isOpen={deleteDialogOpen}
        title="Delete Document"
        message="Are you sure you want to delete this document? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
        onConfirm={confirmDelete}
        onCancel={() => {
          setDeleteDialogOpen(false);
          setDocumentToDelete(null);
        }}
      />

      {/* Create Organization Modal */}
      <CreateOrganizationModal
        isOpen={showCreateOrgModal}
        onClose={() => setShowCreateOrgModal(false)}
        onCreate={handleCreateOrganization}
      />
    </div>
  );
};

export default Dashboard;