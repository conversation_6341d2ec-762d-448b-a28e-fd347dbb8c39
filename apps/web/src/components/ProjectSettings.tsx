import React, { useState } from 'react';
import { 
  <PERSON>older<PERSON><PERSON>, 
  Users, 
  Shield, 
  Archive,
  Copy,
  Trash2,
  Plus,
  ChevronLeft,
  Settings
} from 'lucide-react';
import { Project } from '@docmanager/shared';

interface ProjectSettingsProps {
  project: Project;
  onBack: () => void;
  onUpdate: (updates: Partial<Project>) => void;
  onDelete: (projectId: string) => void;
}

type ProjectSettingsSection = 'general' | 'permissions' | 'archive' | 'integrations';

const ProjectSettings: React.FC<ProjectSettingsProps> = ({
  project,
  onBack,
  onUpdate,
  onDelete
}) => {
  const [activeSection, setActiveSection] = useState<ProjectSettingsSection>('general');
  const [projectName, setProjectName] = useState(project.name);
  const [projectDescription, setProjectDescription] = useState(project.description || '');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const sidebarItems = [
    { id: 'general', label: 'General', icon: <FolderOpen className="h-4 w-4" /> },
    { id: 'permissions', label: 'Permissions', icon: <Users className="h-4 w-4" /> },
    { id: 'archive', label: 'Archive', icon: <Archive className="h-4 w-4" /> },
    { id: 'integrations', label: 'Integrations', icon: <Settings className="h-4 w-4" /> }
  ];

  const handleSaveProject = () => {
    onUpdate({ 
      name: projectName,
      description: projectDescription
    });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const renderGeneralSection = () => (
    <div className="space-y-8">
      {/* Project Information */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Project Information</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Project Name
            </label>
            <input
              type="text"
              value={projectName}
              onChange={(e) => setProjectName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={projectDescription}
              onChange={(e) => setProjectDescription(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Brief description of your project..."
            />
          </div>

          <button
            onClick={handleSaveProject}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Save Changes
          </button>
        </div>
      </div>

      {/* Project Settings */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Project Settings</h2>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">Document Retention</h3>
              <p className="text-sm text-gray-500">How long to keep documents in this project</p>
            </div>
            <select className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="365">1 Year</option>
              <option value="730">2 Years</option>
              <option value="1095">3 Years</option>
              <option value="-1">Forever</option>
            </select>
          </div>

          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">File Type Restrictions</h3>
              <p className="text-sm text-gray-500">Allowed file types for uploads</p>
            </div>
            <button className="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 transition-colors">
              Configure
            </button>
          </div>

          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">Project Status</h3>
              <p className="text-sm text-gray-500">Current project status</p>
            </div>
            <select className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="active">Active</option>
              <option value="archived">Archived</option>
            </select>
          </div>
        </div>
      </div>

      {/* Debug Information */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Debug Information</h2>
        <div className="mb-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Project Metadata</span>
            <button
              onClick={() => copyToClipboard(JSON.stringify({
                name: project.name,
                id: project.id,
                organizationId: project.organizationId,
                status: project.status
              }, null, 2))}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg font-mono text-sm">
          <pre className="text-gray-800">
{`{
  name: "${project.name}",
  id: "${project.id}",
  organizationId: "${project.organizationId}",
  status: "${project.status}"
}`}
          </pre>
        </div>
      </div>

      {/* Danger Zone */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Danger Zone</h2>
        <div className="border border-red-200 rounded-lg p-6">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Delete this project</h3>
              <p className="text-sm text-gray-600">
                Once you delete a project, there is no going back. All documents and data will be permanently removed.
              </p>
            </div>
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="px-4 py-2 border border-red-300 text-red-700 rounded-md hover:bg-red-50 transition-colors"
            >
              Delete Project
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPermissionsSection = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900">Project Permissions</h2>
      
      <div className="space-y-4">
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="font-medium text-gray-900 mb-4">Access Control</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <span className="font-medium text-gray-900">Public Access</span>
                <p className="text-sm text-gray-500">Allow anyone in the organization to view this project</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <span className="font-medium text-gray-900">Guest Upload</span>
                <p className="text-sm text-gray-500">Allow guests to upload documents to this project</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="font-medium text-gray-900 mb-4">Project Members</h3>
          <p className="text-sm text-gray-500 mb-4">Manage who has access to this project</p>
          <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
            <Plus className="h-4 w-4" />
            <span>Add Member</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderOtherSections = (title: string) => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
      <div className="bg-gray-50 p-8 rounded-lg text-center">
        <p className="text-gray-500">This section is coming soon.</p>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'general':
        return renderGeneralSection();
      case 'permissions':
        return renderPermissionsSection();
      case 'archive':
        return renderOtherSections('Archive Settings');
      case 'integrations':
        return renderOtherSections('Integrations');
      default:
        return renderGeneralSection();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white border-r border-gray-200 flex-shrink-0">
        <div className="p-6">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors mb-6"
          >
            <ChevronLeft className="h-4 w-4" />
            <span>Back</span>
          </button>
          <h1 className="text-xl font-semibold text-gray-900 mb-6">Project Settings</h1>
          <nav className="space-y-1">
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id as ProjectSettingsSection)}
                className={`w-full flex items-center space-x-3 px-3 py-2 text-left rounded-md transition-colors ${
                  activeSection === item.id
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                {item.icon}
                <span>{item.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8">
        <div className="max-w-4xl">
          {renderContent()}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowDeleteConfirm(false)} />
            <div className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
              <div className="sm:flex sm:items-start">
                <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                  <Trash2 className="h-6 w-6 text-red-600" />
                </div>
                <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                  <h3 className="text-base font-semibold leading-6 text-gray-900">
                    Delete Project
                  </h3>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      Are you sure you want to delete this project? This action cannot be undone and will permanently remove all documents and data.
                    </p>
                  </div>
                </div>
              </div>
              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto"
                  onClick={() => {
                    onDelete(project.id);
                    setShowDeleteConfirm(false);
                  }}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectSettings;