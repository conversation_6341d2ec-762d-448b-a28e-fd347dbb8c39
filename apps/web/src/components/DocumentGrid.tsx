import React from 'react';
import { File, Calendar, Download, Trash2, Tag } from 'lucide-react';
import { Document } from '../types/Document';
import { formatFileSize, formatDate } from '@docmanager/shared';

interface DocumentGridProps {
  documents: Document[];
  onDelete?: (id: string) => void;
}

const DocumentGrid: React.FC<DocumentGridProps> = ({ documents, onDelete }) => {
  const getFileIcon = (type: Document['type']) => {
    switch (type) {
      case 'PDF':
        return <File className="h-8 w-8 text-red-500" />;
      case 'DOCX':
        return <File className="h-8 w-8 text-blue-500" />;
      case 'Markdown':
        return <File className="h-8 w-8 text-green-500" />;
      case 'TXT':
        return <File className="h-8 w-8 text-gray-500" />;
      case 'CSV':
        return <File className="h-8 w-8 text-orange-500" />;
      default:
        return <File className="h-8 w-8 text-gray-400" />;
    }
  };

  const getTypeColor = (type: Document['type']) => {
    switch (type) {
      case 'PDF':
        return 'bg-red-100 text-red-800';
      case 'DOCX':
        return 'bg-blue-100 text-blue-800';
      case 'Markdown':
        return 'bg-green-100 text-green-800';
      case 'TXT':
        return 'bg-gray-100 text-gray-800';
      case 'CSV':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (documents.length === 0) {
    return (
      <div className="text-center py-12">
        <File className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No documents</h3>
        <p className="mt-1 text-sm text-gray-500">
          No documents match your current filters.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {documents.map((document) => (
        <div
          key={document.id}
          className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow group"
        >
          {/* File Icon and Actions */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-shrink-0">
              {getFileIcon(document.type)}
            </div>
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                title="Download"
              >
                <Download className="h-4 w-4" />
              </button>
              {onDelete && (
                <button
                  onClick={() => onDelete(document.id)}
                  className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                  title="Delete"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>

          {/* Document Name */}
          <h3 className="text-sm font-medium text-gray-900 mb-2 line-clamp-2">
            {document.name}
          </h3>

          {/* Type Badge */}
          <div className="mb-3">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(document.type)}`}>
              {document.type}
            </span>
          </div>

          {/* Document Info */}
          <div className="space-y-2 text-xs text-gray-500">
            <div className="flex items-center">
              <Calendar className="h-3 w-3 mr-2" />
              <span>{formatDate(document.uploadDate)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>{formatFileSize(document.size)}</span>
              <span className="text-gray-400">{document.category}</span>
            </div>
          </div>

          {/* Tags */}
          {document.tags.length > 0 && (
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center flex-wrap gap-1">
                <Tag className="h-3 w-3 text-gray-400" />
                {document.tags.slice(0, 2).map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-gray-100 text-gray-700"
                  >
                    {tag}
                  </span>
                ))}
                {document.tags.length > 2 && (
                  <span className="text-xs text-gray-500">
                    +{document.tags.length - 2} more
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default DocumentGrid;