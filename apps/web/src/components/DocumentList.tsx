import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Trash2, FileText, Tag, Calendar } from 'lucide-react';
import { DataTable } from '@docmanager/ui';
import { Document } from '../types/Document';
import { formatFileSize, formatDate } from '@docmanager/shared';

interface DocumentListProps {
  documents: Document[];
  onDelete?: (id: string) => void;
  className?: string;
  tableId?: string;
}

/**
 * Document list component using the reusable DataTable
 * Features: sorting, pagination, custom cell rendering, and delete actions
 */
const DocumentList: React.FC<DocumentListProps> = ({ 
  documents, 
  onDelete,
  className,
  tableId = 'documents-table'
}) => {
  const getTypeColor = (type: Document['type']) => {
    switch (type) {
      case 'PDF':
        return 'bg-red-100 text-red-800';
      case 'DOCX':
        return 'bg-blue-100 text-blue-800';
      case 'Markdown':
        return 'bg-green-100 text-green-800';
      case 'TXT':
        return 'bg-gray-100 text-gray-800';
      case 'CSV':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns: ColumnDef<Document>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <FileText className="h-4 w-4 text-gray-500 flex-shrink-0" />
          <span className="text-sm font-medium text-gray-900 truncate">
            {row.original.name}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(
            row.original.type
          )}`}
        >
          {row.original.type}
        </span>
      ),
    },
    {
      accessorKey: 'size',
      header: 'Size',
      cell: ({ row }) => (
        <span className="text-sm text-gray-500">
          {formatFileSize(row.original.size)}
        </span>
      ),
    },
    {
      accessorKey: 'category',
      header: 'Category',
      cell: ({ row }) => (
        <span className="text-sm text-gray-700">
          {row.original.category}
        </span>
      ),
    },
    {
      accessorKey: 'tags',
      header: 'Tags',
      cell: ({ row }) => (
        <div className="flex flex-wrap gap-1">
          {row.original.tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-700"
              aria-label={`Delete ${row.original.name}`}
            >
              <Tag className="h-3 w-3 mr-1" />
              {tag}
            </span>
          ))}
          {row.original.tags.length > 3 && (
            <span className="text-xs text-gray-500">
              +{row.original.tags.length - 3} more
            </span>
          )}
        </div>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'uploadDate',
      header: 'Upload Date',
      cell: ({ row }) => (
        <div className="flex items-center space-x-1 text-sm text-gray-500">
          <Calendar className="h-3 w-3" />
          <span>{formatDate(row.original.uploadDate)}</span>
        </div>
      ),
    },
    ...(onDelete
      ? [
          {
            id: 'actions',
            header: 'Actions',
            cell: ({ row }: { row: any }) => (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(row.original.id);
                }}
                className="text-red-600 hover:text-red-900 transition-colors p-1"
                title="Delete document"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            ),
            enableSorting: false,
          } as ColumnDef<Document>,
        ]
      : []),
  ];

  return (
    <DataTable
      data={documents}
      columns={columns}
      emptyMessage="No documents found matching your current filters."
      pageSize={3}
      enableSorting={true}
      enablePagination={true}
      enableStickyPagination={false}
      className={className}
      tableId={tableId}
      showItemsPerPage={true}
      showPageInfo={true}
      showNavigation={true}
      itemsPerPageOptions={[3, 5, 10, 20]}
    />
  );
};

export default DocumentList;