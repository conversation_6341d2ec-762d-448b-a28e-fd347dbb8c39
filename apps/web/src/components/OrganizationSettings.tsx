import React, { useState, useEffect } from 'react';
import { 
  Building2, 
  Users, 
  CreditCard, 
  Shield, 
  FolderOpen, 
  Copy,
  Trash2,
  Plus,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Info
} from 'lucide-react';
import { DataTable } from '@docmanager/ui';
import { ColumnDef } from '@tanstack/react-table';
import { Organization, OrganizationMember } from '@docmanager/shared';

interface OrganizationSettingsProps {
  organization: Organization;
  onBack: () => void;
  onUpdate: (updates: Partial<Organization>) => void;
  onDelete: (orgId: string) => void;
}

type SettingsSection = 'general' | 'members' | 'billing' | 'sso' | 'projects';

const OrganizationSettings: React.FC<OrganizationSettingsProps> = ({
  organization,
  onBack,
  onUpdate,
  onDelete
}) => {
  const [activeSection, setActiveSection] = useState<SettingsSection>('general');
  const [orgName, setOrgName] = useState(organization.name);
  const [members] = useState<OrganizationMember[]>([
    {
      id: '1',
      organizationId: organization.id,
      userId: 'user-1',
      role: 'owner',
      joinedAt: new Date('2024-01-01'),
      status: 'active'
    }
  ]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);

  const sidebarItems = [
    { id: 'general', label: 'General', icon: <Building2 className="h-4 w-4" /> },
    { id: 'members', label: 'Members', icon: <Users className="h-4 w-4" /> },
    { id: 'billing', label: 'Billing', icon: <CreditCard className="h-4 w-4" /> },
    { id: 'sso', label: 'SSO', icon: <Shield className="h-4 w-4" /> },
    { id: 'projects', label: 'Projects', icon: <FolderOpen className="h-4 w-4" /> }
  ];

  const handleSaveOrgName = () => {
    onUpdate({ name: orgName });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const renderGeneralSection = () => (
    <div className="space-y-8">
      {/* Organization Name */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Organization Name</h2>
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <p className="text-sm text-gray-600 mb-4">
            Your Organization is currently named "<strong>{organization.name}</strong>".
          </p>
          <input
            type="text"
            value={orgName}
            onChange={(e) => setOrgName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-4"
          />
          <button
            onClick={handleSaveOrgName}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            Save
          </button>
        </div>
      </div>

      {/* Debug Information */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Debug Information</h2>
        <div className="mb-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Metadata</span>
            <button
              onClick={() => copyToClipboard(JSON.stringify({
                name: organization.name,
                id: organization.id
              }, null, 2))}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg font-mono text-sm">
          <pre className="text-gray-800">
{`{
  name: "${organization.name}",
  id: "${organization.id}"
}`}
          </pre>
        </div>
      </div>

      {/* Danger Zone */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Danger Zone</h2>
        <div className="border border-red-200 rounded-lg p-6">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Delete this organization</h3>
              <p className="text-sm text-gray-600">
                Once you delete an organization, there is no going back. Please be certain.
              </p>
            </div>
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="px-4 py-2 border border-red-300 text-red-700 rounded-md hover:bg-red-50 transition-colors"
            >
              Delete Organization
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMembersSection = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Organization Members</h2>
        <button
          onClick={() => setShowInviteModal(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span>Add new member</span>
        </button>
      </div>

      {/* Members DataTable */}
      <DataTable
        data={members}
        columns={memberColumns}
        pageSize={10}
        enablePagination={true}
        enableStickyPagination={false}
        showItemsPerPage={true}
        showPageInfo={true}
        showNavigation={true}
        itemsPerPageOptions={[5, 10, 25, 50]}
        tableId="members-table"
      />
    </div>
  );

  const renderOtherSections = (title: string) => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
      <div className="bg-gray-50 p-8 rounded-lg text-center">
        <p className="text-gray-500">This section is coming soon.</p>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'general':
        return renderGeneralSection();
      case 'members':
        return renderMembersSection();
      case 'billing':
        return renderOtherSections('Billing');
      case 'sso':
        return renderOtherSections('SSO');
      case 'projects':
        return renderOtherSections('Projects');
      default:
        return renderGeneralSection();
    }
  };

  const memberColumns: ColumnDef<OrganizationMember>[] = [
    {
      accessorKey: 'userId',
      header: 'Name',
      cell: ({ row }) => (
        <div className="flex items-center">
          <div className="h-8 w-8 bg-orange-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
            A
          </div>
          <div className="ml-3">
            <div className="text-sm font-medium text-gray-900">AskInfosec AI</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'userId',
      header: 'Email',
      cell: () => (
        <div className="text-sm text-gray-900"><EMAIL></div>
      ),
    },
    {
      accessorKey: 'role',
      header: () => (
        <div className="flex items-center space-x-1">
          <span>Organization Role</span>
          <Info className="h-3 w-3" />
        </div>
      ),
      cell: ({ row }) => (
        <select 
          value={row.original.role}
          className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="owner">Owner</option>
          <option value="admin">Admin</option>
          <option value="member">Member</option>
          <option value="viewer">Viewer</option>
        </select>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: () => (
        <button className="text-gray-400 hover:text-gray-600 transition-colors">
          <Trash2 className="h-4 w-4" />
        </button>
      ),
      enableSorting: false,
    },
  ];

  return (
    <div className="flex">
      {/* Sidebar */}
      <div className="w-64 bg-white border border-gray-200 rounded-lg mr-6 flex-shrink-0">
        <div className="p-6">
          <h1 className="text-xl font-semibold text-gray-900 mb-6">Organization Settings</h1>
          <nav className="space-y-1">
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id as SettingsSection)}
                className={`w-full flex items-center space-x-3 px-3 py-2 text-left rounded-md transition-colors ${
                  activeSection === item.id
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                {item.icon}
                <span>{item.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        <div className="max-w-4xl">
          {renderContent()}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowDeleteConfirm(false)} />
            <div className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
              <div className="sm:flex sm:items-start">
                <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                  <Trash2 className="h-6 w-6 text-red-600" />
                </div>
                <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                  <h3 className="text-base font-semibold leading-6 text-gray-900">
                    Delete Organization
                  </h3>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      Are you sure you want to delete this organization? This action cannot be undone and will permanently remove all data.
                    </p>
                  </div>
                </div>
              </div>
              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto"
                  onClick={() => {
                    onDelete(organization.id);
                    setShowDeleteConfirm(false);
                  }}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrganizationSettings;