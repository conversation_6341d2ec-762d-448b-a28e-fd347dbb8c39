import React from 'react';
import { X } from 'lucide-react';
import Sidebar from './Sidebar';
import ModuleSidebar from './ModuleSidebar';
import { AppModule } from '../types/AppState';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  activeItem: string;
  onItemClick: (id: string) => void;
  currentModule: AppModule;
  onModuleSelect: (module: AppModule) => void;
}

const MobileMenu: React.FC<MobileMenuProps> = ({
  isOpen,
  onClose,
  activeItem,
  onItemClick,
  currentModule,
  onModuleSelect
}) => {
  const handleItemClick = (id: string) => {
    onItemClick(id);
    onClose();
  };

  const handleModuleSelect = (module: AppModule) => {
    onModuleSelect(module);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 lg:hidden">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity"
        onClick={onClose}
      />
      
      {/* Sidebar */}
      <div className="relative flex flex-col w-full max-w-xs h-full bg-white shadow-xl">
        {/* Close button */}
        <div className="absolute top-0 right-0 -mr-12 pt-2">
          <button
            type="button"
            className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
            onClick={onClose}
          >
            <span className="sr-only">Close sidebar</span>
            <X className="h-6 w-6 text-white" />
          </button>
        </div>

        {/* Sidebar content */}
        {currentModule === 'main-dashboard' ? (
          <ModuleSidebar
            isCollapsed={false}
            currentModule={currentModule}
            onModuleSelect={handleModuleSelect}
          />
        ) : (
          <Sidebar
            isCollapsed={false}
            activeItem={activeItem}
            onItemClick={handleItemClick}
            onBackToDashboard={() => {
              onModuleSelect('main-dashboard');
              onClose();
            }}
          />
        )}
      </div>
    </div>
  );
};

export default MobileMenu;