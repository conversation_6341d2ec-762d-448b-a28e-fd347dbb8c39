import React, { useRef, useMemo } from 'react';
import { ColumnDef, SortingState, PaginationState } from '@tanstack/react-table';
import { DataTable, DataTableProps } from '@docmanager/ui';
import { useDataTableQueryParams } from '../hooks/useQueryParams';

interface DataTableWithQueryParamsProps<TData> extends Omit<DataTableProps<TData>, 'initialPage' | 'initialPageSize' | 'initialSorting' | 'onPaginationChange' | 'onSortingChange'> {
  // Additional props for filtering
  onFilterChange?: (filters: {
    search?: string;
    category?: string;
    type?: string;
    dateFrom?: string;
    dateTo?: string;
  }) => void;
}

/**
 * DataTable component with built-in query parameter support
 * Automatically syncs table state (pagination, sorting) with URL query parameters
 */
export function DataTableWithQueryParams<TData>({
  data,
  columns,
  pageSize = 10,
  onFilterChange,
  ...props
}: DataTableWithQueryParamsProps<TData>) {
  const { params, setParam, setMultipleParams } = useDataTableQueryParams(pageSize);
  const isInitializing = useRef(true);

  // Convert query params to table state
  const initialSorting: SortingState = useMemo(() => {
    if (params.sortBy && params.sortOrder) {
      return [{
        id: params.sortBy,
        desc: params.sortOrder === 'desc'
      }];
    }
    return [];
  }, [params.sortBy, params.sortOrder]);

  const initialPagination = useMemo(() => ({
    pageIndex: params.page - 1, // Convert 1-based to 0-based
    pageSize: params.pageSize
  }), [params.page, params.pageSize]);

  // Handle pagination changes
  const handlePaginationChange = (pagination: PaginationState) => {
    if (isInitializing.current) {
      isInitializing.current = false;
      return;
    }

    setMultipleParams({
      page: pagination.pageIndex + 1, // Convert 0-based to 1-based
      pageSize: pagination.pageSize
    });
  };

  // Handle sorting changes
  const handleSortingChange = (sorting: SortingState) => {
    if (isInitializing.current) {
      return;
    }

    if (sorting.length > 0) {
      const sort = sorting[0];
      setMultipleParams({
        sortBy: sort.id,
        sortOrder: sort.desc ? 'desc' : 'asc'
      });
    } else {
      setMultipleParams({
        sortBy: null,
        sortOrder: null
      });
    }
  };



  return (
    <DataTable
      {...props}
      data={data}
      columns={columns}
      pageSize={pageSize}

      initialPage={initialPagination.pageIndex}
      initialPageSize={initialPagination.pageSize}
      initialSorting={initialSorting}
      onPaginationChange={handlePaginationChange}
      onSortingChange={handleSortingChange}
    />
  );
}

// Export the query params hook for use in filter components
export { useDataTableQueryParams } from '../hooks/useQueryParams';
