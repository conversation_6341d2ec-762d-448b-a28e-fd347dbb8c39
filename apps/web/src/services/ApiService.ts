// Future API integration service
// This structure is ready for real API implementation

interface ApiConfig {
  baseUrl: string;
  timeout: number;
}

class ApiService {
  private config: ApiConfig;

  constructor(config: ApiConfig) {
    this.config = config;
  }

  // All API calls should go through a proxy backend to keep API keys secure
  // Example proxy endpoints:
  // GET /api/documents - List documents
  // POST /api/documents - Upload document
  // DELETE /api/documents/:id - Delete document
  // GET /api/documents/search - Search documents

  async get<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.config.baseUrl}${endpoint}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Never include API keys in frontend code
        // Authentication should be handled by proxy backend
      },
      signal: AbortSignal.timeout(this.config.timeout)
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(`${this.config.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      signal: AbortSignal.timeout(this.config.timeout)
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async uploadFile<T>(endpoint: string, file: File): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${this.config.baseUrl}${endpoint}`, {
      method: 'POST',
      body: formData,
      signal: AbortSignal.timeout(this.config.timeout)
    });

    if (!response.ok) {
      throw new Error(`Upload Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.config.baseUrl}${endpoint}`, {
      method: 'DELETE',
      signal: AbortSignal.timeout(this.config.timeout)
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }
}

// Example configuration for production
export const apiService = new ApiService({
  baseUrl: process.env.NODE_ENV === 'production' 
    ? 'https://your-api-domain.com' 
    : 'http://localhost:3001',
  timeout: 10000
});

/*
Proxy Backend Structure (Express.js example):

const express = require('express');
const multer = require('multer');
const app = express();

// Secure API key handling - stored in environment variables
const API_KEY = process.env.EXTERNAL_API_KEY;

// Upload endpoint
app.post('/api/documents', upload.single('file'), async (req, res) => {
  try {
    // Process file upload
    // Make authenticated request to external service using API_KEY
    // Return sanitized response to frontend
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// List documents endpoint
app.get('/api/documents', async (req, res) => {
  try {
    // Make authenticated request to external service
    // Filter/transform data as needed
    // Return sanitized response
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

This ensures API keys never reach the frontend and all external
communication is handled securely on the server side.
*/