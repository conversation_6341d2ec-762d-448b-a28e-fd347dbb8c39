import { Document, DocumentService as SharedDocumentService, formatFileSize, formatDate } from '@docmanager/shared';

export class DocumentService extends SharedDocumentService {
  private static instance: DocumentService;

  private constructor() {
    super();
  }

  static getInstance(): DocumentService {
    if (!DocumentService.instance) {
      DocumentService.instance = new DocumentService();
    }
    return DocumentService.instance;
  }

  async uploadDocument(file: File): Promise<Document> {
    
    const fileType = this.getFileType(file.name);
    const newDocument = await this.createDocument({
      name: file.name,
      type: fileType,
      size: file.size,
      category: 'Uncategorized',
      tags: [],
      organizationId: '1', // This should come from context in real implementation
      projectId: undefined
    });
    
    // Add the file URL for local files
    newDocument.url = URL.createObjectURL(file);
    return newDocument;
  }

  private getFileType(filename: string): Document['type'] {
    const extension = filename.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'pdf':
        return 'PDF';
      case 'doc':
      case 'docx':
        return 'DOCX';
      case 'txt':
        return 'TXT';
      case 'md':
        return 'Markdown';
      case 'csv':
        return 'CSV';
      case 'xls':
      case 'xlsx':
      case 'ppt':
      case 'pptx':
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      default:
        return 'PDF';
    }
  }

  formatFileSize = formatFileSize;
  formatDate = formatDate;
}

export default DocumentService;