import { useState, useEffect, useCallback } from 'react';

export interface QueryParams {
  [key: string]: string | number | boolean | null | undefined;
}

/**
 * Custom hook for managing URL query parameters
 * Provides methods to get, set, and sync query parameters with component state
 */
export function useQueryParams<T extends QueryParams>(
  defaultParams: T,
  options: {
    // Whether to update URL immediately or batch updates
    immediate?: boolean;
    // Whether to replace or push to history
    replace?: boolean;
  } = {}
) {
  const { immediate = true, replace = true } = options;

  // Parse current URL parameters
  const parseUrlParams = useCallback((): T => {
    if (typeof window === 'undefined') return defaultParams;
    
    const urlParams = new URLSearchParams(window.location.search);
    const params = { ...defaultParams };
    
    Object.keys(defaultParams).forEach((key) => {
      const value = urlParams.get(key);
      if (value !== null) {
        const defaultValue = defaultParams[key];
        
        // Type conversion based on default value type
        if (typeof defaultValue === 'number') {
          const numValue = Number(value);
          if (!isNaN(numValue)) {
            (params as any)[key] = numValue;
          }
        } else if (typeof defaultValue === 'boolean') {
          (params as any)[key] = value === 'true';
        } else {
          (params as any)[key] = value;
        }
      }
    });
    
    return params;
  }, [defaultParams]);

  const [params, setParams] = useState<T>(parseUrlParams);

  // Update URL when params change
  const updateUrl = useCallback((newParams: T) => {
    if (typeof window === 'undefined') return;
    
    const urlParams = new URLSearchParams();
    
    Object.entries(newParams).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== defaultParams[key]) {
        urlParams.set(key, String(value));
      }
    });
    
    const newUrl = urlParams.toString() 
      ? `${window.location.pathname}?${urlParams.toString()}`
      : window.location.pathname;
    
    if (replace) {
      window.history.replaceState({}, '', newUrl);
    } else {
      window.history.pushState({}, '', newUrl);
    }
  }, [defaultParams, replace]);

  // Set individual parameter
  const setParam = useCallback((key: keyof T, value: T[keyof T]) => {
    const newParams = { ...params, [key]: value };
    setParams(newParams);
    
    if (immediate) {
      updateUrl(newParams);
    }
  }, [params, immediate, updateUrl]);

  // Set multiple parameters at once
  const setMultipleParams = useCallback((updates: Partial<T>) => {
    const newParams = { ...params, ...updates };
    setParams(newParams);
    
    if (immediate) {
      updateUrl(newParams);
    }
  }, [params, immediate, updateUrl]);

  // Reset to default parameters
  const resetParams = useCallback(() => {
    setParams(defaultParams);
    if (immediate) {
      updateUrl(defaultParams);
    }
  }, [defaultParams, immediate, updateUrl]);

  // Manual URL update (for batched updates)
  const commitToUrl = useCallback(() => {
    updateUrl(params);
  }, [params, updateUrl]);

  // Listen for browser back/forward navigation
  useEffect(() => {
    const handlePopState = () => {
      setParams(parseUrlParams());
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [parseUrlParams]);

  // Initial URL sync
  useEffect(() => {
    const urlParams = parseUrlParams();
    setParams(urlParams);
  }, [parseUrlParams]);

  return {
    params,
    setParam,
    setMultipleParams,
    resetParams,
    commitToUrl,
  };
}

/**
 * Specialized hook for DataTable query parameters
 */
export interface DataTableQueryParams {
  page: number;
  pageSize: number;
  sortBy: string | null;
  sortOrder: 'asc' | 'desc' | null;
  search: string | null;
  category: string | null;
  type: string | null;
  dateFrom: string | null;
  dateTo: string | null;
}

export function useDataTableQueryParams(defaultPageSize: number = 10) {
  const defaultParams: DataTableQueryParams = {
    page: 1,
    pageSize: defaultPageSize,
    sortBy: null,
    sortOrder: null,
    search: null,
    category: null,
    type: null,
    dateFrom: null,
    dateTo: null,
  };

  return useQueryParams(defaultParams);
}
