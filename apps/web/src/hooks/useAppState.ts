import { useState, useCallback } from 'react';
import { useEffect } from 'react';
import { AppState, AppModule } from '../types/AppState';

const STORAGE_KEY = 'docmanager_app_state';

const loadStateFromStorage = (): Partial<AppState> => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error('Failed to load app state from storage:', error);
    return {};
  }
};

const saveStateToStorage = (state: AppState) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify({
      currentModule: state.currentModule,
      organization: {
        currentOrganizationId: state.organization.currentOrganizationId,
        currentProjectId: state.organization.currentProjectId
      },
      documentManagement: {
        activeView: state.documentManagement.activeView,
        viewMode: state.documentManagement.viewMode
      }
    }));
  } catch (error) {
    console.error('Failed to save app state to storage:', error);
  }
};

const initialState: AppState = {
  currentModule: 'main-dashboard',
  organization: {
    currentOrganizationId: '1', // Default to first organization
    currentProjectId: null,
    availableOrganizations: [],
    availableProjects: [],
    userRole: null
  },
  documentManagement: {
    activeView: 'all-documents',
    viewMode: 'grid',
    showUpload: false
  },
  settings: {
    selectedProject: null
  }
};

export const useAppState = () => {
  const [appState, setAppState] = useState<AppState>(() => {
    const storedState = loadStateFromStorage();
    return {
      ...initialState,
      ...storedState
    };
  });

  // Save state to localStorage whenever it changes
  useEffect(() => {
    saveStateToStorage(appState);
  }, [appState]);

  const setCurrentModule = useCallback((module: AppModule) => {
    setAppState(prev => ({
      ...prev,
      currentModule: module
    }));
  }, []);

  const updateDocumentManagement = useCallback((updates: Partial<AppState['documentManagement']>) => {
    setAppState(prev => ({
      ...prev,
      documentManagement: {
        ...prev.documentManagement,
        ...updates
      }
    }));
  }, []);

  const updateOrganization = useCallback((updates: Partial<AppState['organization']>) => {
    setAppState(prev => ({
      ...prev,
      organization: {
        ...prev.organization,
        ...updates
      }
    }));
  }, []);

  const updateSettings = useCallback((updates: Partial<AppState['settings']>) => {
    setAppState(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        ...updates
      }
    }));
  }, []);

  return {
    appState,
    setCurrentModule,
    updateDocumentManagement,
    updateOrganization,
    updateSettings
  };
};