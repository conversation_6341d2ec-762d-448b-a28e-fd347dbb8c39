export type AppModule = 'main-dashboard' | 'document-management' | 'organization-settings' | 'project-settings';

export interface OrganizationState {
  currentOrganizationId: string | null;
  currentProjectId: string | null;
  availableOrganizations: any[];
  availableProjects: any[];
  userRole: string | null;
}

export interface AppState {
  currentModule: AppModule;
  organization: OrganizationState;
  documentManagement: {
    activeView: string;
    viewMode: 'grid' | 'list';
    showUpload: boolean;
  };
  settings: {
    selectedProject: string | null;
  };
}

export interface ModuleConfig {
  id: AppModule;
  name: string;
  description: string;
  icon: React.ReactNode;
  path: string;
}