import { useState, useRef, useEffect } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  ColumnDef,
  flexRender,
  SortingState,
  PaginationState,
} from '@tanstack/react-table';
import {
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';
import { TablePaginationFooter } from './TablePaginationFooter';

export interface DataTableProps<TData> {
  data: TData[];
  columns: ColumnDef<TData>[];
  pageSize?: number;
  enableSorting?: boolean;
  enablePagination?: boolean;
  enableStickyPagination?: boolean;
  onRowClick?: (row: TData) => void;
  className?: string;
  emptyMessage?: string;
  loading?: boolean;
  showItemsPerPage?: boolean;
  showPageInfo?: boolean;
  showNavigation?: boolean;
  itemsPerPageOptions?: number[];
  tableId?: string;
  sidebarCollapsed?: boolean;
  // Query parameter support
  initialPage?: number;
  initialPageSize?: number;
  initialSorting?: SortingState;
  onPaginationChange?: (pagination: PaginationState) => void;
  onSortingChange?: (sorting: SortingState) => void;
}

/**
 * Generic DataTable component built on TanStack Table
 * Features:
 * - Sorting (click column headers)
 * - Pagination with sticky footer
 * - Custom cell rendering
 * - Loading and empty states
 * - Responsive design
 * - Full TypeScript support
 */
export function DataTable<TData>({
  data,
  columns,
  pageSize = 10,
  enableSorting = true,
  enablePagination = true,
  enableStickyPagination = true,
  onRowClick,
  className = '',
  emptyMessage = 'No data available',
  loading = false,
  showItemsPerPage = true,
  showPageInfo = true,
  showNavigation = true,
  itemsPerPageOptions = [10, 20, 50, 100],
  tableId,
  sidebarCollapsed = false,
  initialPage = 0,
  initialPageSize,
  initialSorting = [],
  onPaginationChange,
  onSortingChange,
}: DataTableProps<TData>) {
  const [sorting, setSorting] = useState<SortingState>(initialSorting);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: initialPage,
    pageSize: initialPageSize || pageSize,
  });
  const isInitializing = useRef(true);

  // Wrapper functions to handle both internal state and external callbacks
  const handleSortingChange = (updaterOrValue: SortingState | ((old: SortingState) => SortingState)) => {
    const newSorting = typeof updaterOrValue === 'function' ? updaterOrValue(sorting) : updaterOrValue;
    setSorting(newSorting);

    // Only call external callback if not initializing
    if (!isInitializing.current) {
      onSortingChange?.(newSorting);
    }
  };

  const handlePaginationChange = (updaterOrValue: PaginationState | ((old: PaginationState) => PaginationState)) => {
    const newPagination = typeof updaterOrValue === 'function' ? updaterOrValue(pagination) : updaterOrValue;
    setPagination(newPagination);

    // Only call external callback if not initializing
    if (!isInitializing.current) {
      onPaginationChange?.(newPagination);
    }
  };

  // Mark initialization as complete after first render
  useEffect(() => {
    isInitializing.current = false;
  }, []);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      pagination,
    },
    onSortingChange: handleSortingChange,
    onPaginationChange: handlePaginationChange,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: enableSorting ? getSortedRowModel() : undefined,
    getPaginationRowModel: enablePagination ? getPaginationRowModel() : undefined,
    // Ensure consistent pagination behavior
    manualPagination: false,
    pageCount: enablePagination ? Math.ceil(data.length / pageSize) : undefined,
  });

  if (loading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 overflow-hidden ${className}`}>
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 overflow-hidden ${className}`}>
        <div className="p-8 text-center">
          <p className="text-gray-500">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div 
        className={`bg-white rounded-lg border border-gray-200 overflow-hidden ${className}`}
        style={enableStickyPagination && enablePagination ? { marginBottom: '100px' } : {}}
      >
        {/* Table Container with Horizontal Scroll */}
        <div className="overflow-x-auto">
          <table 
            className="min-w-full divide-y divide-gray-200"
            id={tableId}
            role="table"
            aria-label="Data table"
          >
            <thead className="bg-gray-50">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id} role="row">
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      scope="col"
                      className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                        header.column.getCanSort() ? 'cursor-pointer select-none hover:bg-gray-100' : ''
                      }`}
                      onClick={header.column.getToggleSortingHandler()}
                      role="columnheader"
                      aria-sort={
                        header.column.getIsSorted() === 'asc' ? 'ascending' :
                        header.column.getIsSorted() === 'desc' ? 'descending' : 'none'
                      }
                      tabIndex={header.column.getCanSort() ? 0 : undefined}
                      onKeyDown={(e) => {
                        if (header.column.getCanSort() && (e.key === 'Enter' || e.key === ' ')) {
                          e.preventDefault();
                          header.column.getToggleSortingHandler()?.(e);
                        }
                      }}
                    >
                      <div className="flex items-center space-x-1">
                        <span>
                          {header.isPlaceholder
                            ? null
                            : flexRender(header.column.columnDef.header, header.getContext())}
                        </span>
                        {header.column.getCanSort() && (
                          <span className="flex flex-col" aria-hidden="true">
                            {header.column.getIsSorted() === 'asc' ? (
                              <ChevronUp className="h-3 w-3 text-gray-900" />
                            ) : header.column.getIsSorted() === 'desc' ? (
                              <ChevronDown className="h-3 w-3 text-gray-900" />
                            ) : (
                              <div className="flex flex-col">
                                <ChevronUp className="h-3 w-3 text-gray-400" />
                                <ChevronDown className="h-3 w-3 text-gray-400 -mt-1" />
                              </div>
                            )}
                          </span>
                        )}
                      </div>
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {table.getRowModel().rows.map((row) => (
                <tr
                  key={row.id}
                  className={`${
                    onRowClick ? 'cursor-pointer hover:bg-gray-50 transition-colors' : ''
                  }`}
                  onClick={() => onRowClick?.(row.original)}
                  role="row"
                  tabIndex={onRowClick ? 0 : undefined}
                  onKeyDown={(e) => {
                    if (onRowClick && (e.key === 'Enter' || e.key === ' ')) {
                      e.preventDefault();
                      onRowClick(row.original);
                    }
                  }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td key={cell.id} className="px-6 py-4 whitespace-nowrap" role="cell">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

      </div>

      {/* Simple Pagination Footer - Only render when sticky pagination is disabled */}
      {enablePagination && !enableStickyPagination && (
        <div className="bg-white border border-gray-200 rounded-b-lg px-4 py-3 sm:px-6">
          <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            {/* Left side - Items info and per page selector */}
            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-6">
              {/* Items per page selector */}
              {showItemsPerPage && (
                <div className="flex items-center space-x-2">
                  <label htmlFor={`items-per-page-${tableId}`} className="text-sm text-gray-700 whitespace-nowrap">
                    Items per page:
                  </label>
                  <select
                    id={`items-per-page-${tableId}`}
                    value={pagination.pageSize}
                    onChange={(e) => {
                      table.setPageSize(Number(e.target.value));
                    }}
                    className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {itemsPerPageOptions.map((size) => (
                      <option key={size} value={size}>
                        {size}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Items range info */}
              {showPageInfo && (
                <div className="text-sm text-gray-700">
                  Showing <span className="font-medium">{pagination.pageIndex * pagination.pageSize + 1}</span> to{' '}
                  <span className="font-medium">{Math.min((pagination.pageIndex + 1) * pagination.pageSize, data.length)}</span> of{' '}
                  <span className="font-medium">{data.length}</span> entries
                </div>
              )}
            </div>

            {/* Right side - Navigation controls */}
            {showNavigation && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => table.setPageIndex(0)}
                  disabled={!table.getCanPreviousPage()}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-md hover:bg-gray-100"
                  title="Go to first page"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </button>
                <button
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-md hover:bg-gray-100"
                  title="Go to previous page"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
                
                <span className="text-sm text-gray-700">
                  Page {pagination.pageIndex + 1} of {table.getPageCount()}
                </span>
                
                <button
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-md hover:bg-gray-100"
                  title="Go to next page"
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
                <button
                  onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                  disabled={!table.getCanNextPage()}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-md hover:bg-gray-100"
                  title="Go to last page"
                >
                  <ChevronsRight className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Sticky Pagination Footer - Render outside container */}
      {enablePagination && enableStickyPagination && data.length > 0 && (
        <TablePaginationFooter
          table={table}
          totalItems={data.length}
          showItemsPerPage={showItemsPerPage}
          showPageInfo={showPageInfo}
          showNavigation={showNavigation}
          itemsPerPageOptions={itemsPerPageOptions}
          sidebarCollapsed={sidebarCollapsed}
        />
      )}
    </>
  );
}