import { useState } from 'react';
import { Table } from '@tanstack/react-table';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  MoreHorizontal,
} from 'lucide-react';

export interface TablePaginationFooterProps<TData> {
  table: Table<TData>;
  totalItems: number;
  className?: string;
  showItemsPerPage?: boolean;
  showPageInfo?: boolean;
  showNavigation?: boolean;
  itemsPerPageOptions?: number[];
  sidebarCollapsed?: boolean;
}

/**
 * Reusable sticky pagination footer component for DataTable
 * Integrates with TanStack Table's pagination API
 * Automatically appears at the bottom of the page when used with DataTable
 */
export function TablePaginationFooter<TData>({
  table,
  totalItems,
  className = '',
  showItemsPerPage = true,
  showPageInfo = true,
  showNavigation = true,
  itemsPerPageOptions = [10, 20, 50, 100],
  sidebarCollapsed = false,
}: TablePaginationFooterProps<TData>) {
  const [isPageInputVisible, setIsPageInputVisible] = useState(false);
  const [pageInputValue, setPageInputValue] = useState('');

  const {
    getState,
    getPageCount,
    getCanPreviousPage,
    getCanNextPage,
    previousPage,
    nextPage,
    setPageIndex,
    setPageSize,
  } = table;

  const { pagination } = getState();
  const { pageIndex, pageSize } = pagination;
  const pageCount = getPageCount();
  const currentPage = pageIndex + 1;
  
  // Calculate displayed items range
  const startItem = totalItems === 0 ? 0 : pageIndex * pageSize + 1;
  const endItem = Math.min((pageIndex + 1) * pageSize, totalItems);

  // Generate page numbers for pagination display
  const generatePageNumbers = () => {
    const pages: (number | 'ellipsis')[] = [];
    const showEllipsis = pageCount > 7;

    if (!showEllipsis) {
      // Show all pages if total pages <= 7
      for (let i = 1; i <= pageCount; i++) {
        pages.push(i);
      }
    } else {
      // Complex pagination with ellipsis
      if (currentPage <= 4) {
        // Show first 5 pages + ellipsis + last page
        for (let i = 1; i <= 5; i++) {
          pages.push(i);
        }
        if (pageCount > 6) {
          pages.push('ellipsis');
          pages.push(pageCount);
        }
      } else if (currentPage >= pageCount - 3) {
        // Show first page + ellipsis + last 5 pages
        pages.push(1);
        if (pageCount > 6) {
          pages.push('ellipsis');
        }
        for (let i = pageCount - 4; i <= pageCount; i++) {
          pages.push(i);
        }
      } else {
        // Show first page + ellipsis + current-1, current, current+1 + ellipsis + last page
        pages.push(1);
        pages.push('ellipsis');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('ellipsis');
        pages.push(pageCount);
      }
    }

    return pages;
  };

  const handlePageInputSubmit = () => {
    const pageNumber = parseInt(pageInputValue, 10);
    if (pageNumber >= 1 && pageNumber <= pageCount) {
      setPageIndex(pageNumber - 1);
    }
    setPageInputValue('');
    setIsPageInputVisible(false);
  };

  const handlePageInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handlePageInputSubmit();
    } else if (e.key === 'Escape') {
      setPageInputValue('');
      setIsPageInputVisible(false);
    }
  };

  // Don't render if no data or only one page and no items per page selector
  if (totalItems === 0) {
    return null;
  }

  return (
    <div
      className={`fixed bottom-0 left-0 right-0 z-[9999] bg-white border-t border-gray-200 shadow-lg transition-all duration-300 ${
        sidebarCollapsed ? 'lg:left-20' : 'lg:left-64'
      } ${className}`}
      role="navigation"
      aria-label="Table pagination"
    >
      <div className="px-4 py-3 sm:px-6">
        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          {/* Left side - Items info and per page selector */}
          <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-6">
            {/* Items per page selector */}
            {showItemsPerPage && (
              <div className="flex items-center space-x-2">
                <label htmlFor="items-per-page" className="text-sm text-gray-700 whitespace-nowrap">
                  Items per page:
                </label>
                <select
                  id="items-per-page"
                  value={pageSize}
                  onChange={(e) => {
                    setPageSize(Number(e.target.value));
                    setPageIndex(0); // Reset to first page when changing page size
                  }}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  aria-label="Select number of items per page"
                >
                  {itemsPerPageOptions.map((size) => (
                    <option key={size} value={size}>
                      {size}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Items range info */}
            {showPageInfo && (
              <div className="text-sm text-gray-700" aria-live="polite">
                {totalItems > 0 ? (
                  <>
                    Showing <span className="font-medium">{startItem}</span> to{' '}
                    <span className="font-medium">{endItem}</span> of{' '}
                    <span className="font-medium">{totalItems}</span> entries
                  </>
                ) : (
                  'No entries to show'
                )}
              </div>
            )}
          </div>

          {/* Right side - Navigation controls */}
          {showNavigation && pageCount > 1 && (
            <div className="flex items-center space-x-2">
              {/* First and Previous buttons */}
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => setPageIndex(0)}
                  disabled={!getCanPreviousPage()}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-md hover:bg-gray-100"
                  title="Go to first page"
                  aria-label="Go to first page"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </button>
                <button
                  onClick={() => previousPage()}
                  disabled={!getCanPreviousPage()}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-md hover:bg-gray-100"
                  title="Go to previous page"
                  aria-label="Go to previous page"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
              </div>

              {/* Page numbers */}
              <div className="hidden sm:flex items-center space-x-1">
                {generatePageNumbers().map((page, index) => (
                  <div key={index}>
                    {page === 'ellipsis' ? (
                      <button
                        onClick={() => setIsPageInputVisible(true)}
                        className="px-3 py-1 text-gray-500 hover:text-gray-700 transition-colors"
                        title="Jump to page"
                        aria-label="Jump to specific page"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    ) : (
                      <button
                        onClick={() => setPageIndex(page - 1)}
                        className={`px-3 py-1 text-sm rounded-md transition-colors ${
                          currentPage === page
                            ? 'bg-blue-100 text-blue-700 font-medium'
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                        aria-label={`Go to page ${page}`}
                        aria-current={currentPage === page ? 'page' : undefined}
                      >
                        {page}
                      </button>
                    )}
                  </div>
                ))}
              </div>

              {/* Mobile page info */}
              <div className="sm:hidden text-sm text-gray-700">
                Page {currentPage} of {pageCount}
              </div>

              {/* Next and Last buttons */}
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => nextPage()}
                  disabled={!getCanNextPage()}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-md hover:bg-gray-100"
                  title="Go to next page"
                  aria-label="Go to next page"
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setPageIndex(pageCount - 1)}
                  disabled={!getCanNextPage()}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-md hover:bg-gray-100"
                  title="Go to last page"
                  aria-label="Go to last page"
                >
                  <ChevronsRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Page input modal overlay */}
      {isPageInputVisible && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={() => setIsPageInputVisible(false)}
        >
          <div
            className="bg-white rounded-lg p-6 shadow-xl"
            onClick={(e) => e.stopPropagation()}
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Jump to Page</h3>
            <div className="flex items-center space-x-3">
              <input
                type="number"
                min="1"
                max={pageCount}
                value={pageInputValue}
                onChange={(e) => setPageInputValue(e.target.value)}
                onKeyDown={handlePageInputKeyDown}
                placeholder={`1-${pageCount}`}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                autoFocus
                aria-label="Page number"
              />
              <button
                onClick={handlePageInputSubmit}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
              >
                Go
              </button>
              <button
                onClick={() => setIsPageInputVisible(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors text-sm"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}