{"name": "@docmanager/ui", "version": "0.0.0", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./styles": "./dist/styles.css"}, "scripts": {"build": "tsc && tailwindcss -i ./src/styles.css -o ./dist/styles.css --minify", "dev": "tsc --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "test": "vitest", "clean": "rm -rf dist .turbo"}, "dependencies": {"@docmanager/shared": "*", "@tanstack/react-table": "^8.11.6", "lucide-react": "^0.468.0", "react": "^18.0.0"}, "devDependencies": {"@types/react": "^18.3.17", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.1.0", "tailwindcss": "^3.4.17", "typescript": "^5.7.2", "vitest": "^3.2.4"}, "peerDependencies": {"@tanstack/react-table": "^8.0.0", "react": "^18.0.0"}}