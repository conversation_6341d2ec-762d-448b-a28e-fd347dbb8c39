{"compilerOptions": {"target": "ES2022", "lib": ["ES2023"], "module": "ESNext", "moduleResolution": "bundler", "composite": true, "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}