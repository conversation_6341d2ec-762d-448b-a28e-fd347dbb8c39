import { Document, DocumentFilter, CreateDocumentRequest } from '../types/index';

export class DocumentService {
  private documents: Document[] = [
    {
      id: '1',
      name: 'Project Proposal Q1 2024.pdf',
      type: 'PDF',
      size: 2547328,
      uploadDate: new Date('2024-01-15'),
      category: 'Projects',
      tags: ['proposal', 'q1', 'budget'],
      lastModified: new Date('2024-01-15'),
      url: '#',
      organizationId: '1',
      projectId: '1',
      uploadedBy: 'user-1'
    },
    {
      id: '2',
      name: 'Meeting Notes - Team Sync.md',
      type: 'Markdown',
      size: 15420,
      uploadDate: new Date('2024-01-14'),
      category: 'Meetings',
      tags: ['team', 'sync', 'notes'],
      lastModified: new Date('2024-01-14'),
      url: '#',
      organizationId: '1',
      projectId: '1',
      uploadedBy: 'user-1'
    },
    {
      id: '3',
      name: 'Financial Report December.docx',
      type: 'DOCX',
      size: 891234,
      uploadDate: new Date('2024-01-10'),
      category: 'Finance',
      tags: ['report', 'december', 'financial'],
      lastModified: new Date('2024-01-10'),
      url: '#',
      organizationId: '1',
      projectId: '2',
      uploadedBy: 'user-1'
    },
    {
      id: '4',
      name: 'User Research Data.csv',
      type: 'CSV',
      size: 67890,
      uploadDate: new Date('2024-01-08'),
      category: 'Research',
      tags: ['user', 'research', 'data'],
      lastModified: new Date('2024-01-08'),
      url: '#',
      organizationId: '1',
      projectId: '2',
      uploadedBy: 'user-1'
    },
    {
      id: '5',
      name: 'System Requirements.txt',
      type: 'TXT',
      size: 5432,
      uploadDate: new Date('2024-01-05'),
      category: 'Technical',
      tags: ['system', 'requirements', 'specs'],
      lastModified: new Date('2024-01-05'),
      url: '#',
      organizationId: '1',
      uploadedBy: 'user-1'
    },
    {
      id: '6',
      name: 'API Documentation.md',
      type: 'Markdown',
      size: 34567,
      uploadDate: new Date('2024-01-03'),
      category: 'Technical',
      tags: ['api', 'documentation', 'reference'],
      lastModified: new Date('2024-01-03'),
      url: '#',
      organizationId: '2',
      projectId: '3',
      uploadedBy: 'user-1'
    }
  ];

  // Add more documents to ensure pagination is visible
  private generateAdditionalDocuments(): Document[] {
    const additionalDocs: Document[] = [];
    const categories = ['Marketing', 'Legal', 'HR', 'Operations', 'Sales'];
    const types: Document['type'][] = ['PDF', 'DOCX', 'TXT', 'CSV', 'Markdown'];
    const baseNames = [
      'Quarterly Report', 'Team Meeting Notes', 'Project Proposal', 'Budget Analysis',
      'Marketing Campaign', 'Employee Handbook', 'Sales Forecast', 'Technical Specification',
      'Contract Agreement', 'Performance Review', 'Strategic Plan', 'Risk Assessment'
    ];

    for (let i = 7; i <= 25; i++) {
      const name = baseNames[i % baseNames.length];
      const category = categories[i % categories.length];
      const type = types[i % types.length];
      
      additionalDocs.push({
        id: i.toString(),
        name: `${name} ${Math.floor(i/4) + 1}.${type.toLowerCase()}`,
        type,
        size: Math.floor(Math.random() * 5000000) + 10000, // Random size between 10KB and 5MB
        uploadDate: new Date(2024, 0, Math.floor(Math.random() * 30) + 1),
        category,
        tags: [`tag${i}`, `category-${category.toLowerCase()}`],
        lastModified: new Date(2024, 0, Math.floor(Math.random() * 30) + 1),
        url: '#',
        organizationId: i % 3 === 0 ? '2' : '1',
        projectId: i % 2 === 0 ? '1' : '2',
        uploadedBy: 'user-1'
      });
    }

    return additionalDocs;
  }

  constructor() {
    // Add the additional documents to the main array
    this.documents = [...this.documents, ...this.generateAdditionalDocuments()];
  }

  async getDocuments(organizationId?: string, projectId?: string): Promise<Document[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    let filtered = [...this.documents];
    
    if (organizationId) {
      filtered = filtered.filter(doc => doc.organizationId === organizationId);
    }
    
    if (projectId) {
      filtered = filtered.filter(doc => doc.projectId === projectId);
    }
    
    return filtered;
  }

  async createDocument(request: CreateDocumentRequest): Promise<Document> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));
    
    const newDocument: Document = {
      id: Date.now().toString(),
      name: request.name,
      type: request.type,
      size: request.size,
      uploadDate: new Date(),
      category: request.category,
      tags: request.tags,
      lastModified: new Date(),
      url: '#',
      organizationId: request.organizationId,
      projectId: request.projectId,
      uploadedBy: 'user-1' // In real app, get from auth context
    };

    this.documents.unshift(newDocument);
    return newDocument;
  }

  async deleteDocument(id: string): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100));
    this.documents = this.documents.filter(doc => doc.id !== id);
  }

  filterDocuments(documents: Document[], filter: DocumentFilter): Document[] {
    let filtered = [...documents];

    // Search term filter
    if (filter.searchTerm) {
      const searchLower = filter.searchTerm.toLowerCase();
      filtered = filtered.filter(doc =>
        doc.name.toLowerCase().includes(searchLower) ||
        doc.tags.some(tag => tag.toLowerCase().includes(searchLower)) ||
        doc.category.toLowerCase().includes(searchLower)
      );
    }

    // Type filter
    if (filter.type && filter.type !== 'all') {
      filtered = filtered.filter(doc => doc.type === filter.type);
    }

    // Category filter
    if (filter.category && filter.category !== 'all') {
      filtered = filtered.filter(doc => doc.category === filter.category);
    }

    // Date range filter
    if (filter.dateRange.start) {
      filtered = filtered.filter(doc => doc.uploadDate >= filter.dateRange.start!);
    }
    if (filter.dateRange.end) {
      filtered = filtered.filter(doc => doc.uploadDate <= filter.dateRange.end!);
    }

    // Sort
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (filter.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'date':
          comparison = a.uploadDate.getTime() - b.uploadDate.getTime();
          break;
        case 'size':
          comparison = a.size - b.size;
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
      }

      return filter.sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }

  getCategories(): string[] {
    const categories = new Set(this.documents.map(doc => doc.category));
    return Array.from(categories).sort();
  }
}