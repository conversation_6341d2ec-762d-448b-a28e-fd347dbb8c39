import { 
  Organization, 
  Project, 
  OrganizationMember, 
  CreateOrganizationRequest, 
  CreateProjectRequest,
  OrganizationContext
} from '../types/organization';

export class OrganizationService {
  private organizations: Organization[] = [
    {
      id: '1',
      name: 'Acme Corporation',
      slug: 'acme-corp',
      description: 'Main business organization',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      ownerId: 'user-1',
      settings: {
        maxStorageGB: 100,
        maxUsers: 50,
        features: ['advanced-search', 'api-access', 'custom-branding']
      }
    },
    {
      id: '2',
      name: 'Personal Workspace',
      slug: 'personal',
      description: 'Personal document management',
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
      ownerId: 'user-1',
      settings: {
        maxStorageGB: 10,
        maxUsers: 1,
        features: ['basic-features']
      }
    }
  ];

  private projects: Project[] = [
    {
      id: '1',
      name: 'Q1 2024 Planning',
      slug: 'q1-2024-planning',
      description: 'Strategic planning for Q1',
      organizationId: '1',
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
      createdById: 'user-1',
      status: 'active',
      settings: {
        documentRetentionDays: 365,
        allowedFileTypes: ['.pdf', '.docx', '.md']
      }
    },
    {
      id: '2',
      name: 'Product Launch',
      slug: 'product-launch',
      description: 'New product launch documentation',
      organizationId: '1',
      createdAt: new Date('2024-01-05'),
      updatedAt: new Date('2024-01-05'),
      createdById: 'user-1',
      status: 'active',
      settings: {
        documentRetentionDays: 730
      }
    },
    {
      id: '3',
      name: 'Personal Notes',
      slug: 'personal-notes',
      description: 'My personal document collection',
      organizationId: '2',
      createdAt: new Date('2024-01-16'),
      updatedAt: new Date('2024-01-16'),
      createdById: 'user-1',
      status: 'active',
      settings: {}
    }
  ];

  private members: OrganizationMember[] = [
    {
      id: '1',
      organizationId: '1',
      userId: 'user-1',
      role: 'owner',
      joinedAt: new Date('2024-01-01'),
      status: 'active'
    },
    {
      id: '2',
      organizationId: '2',
      userId: 'user-1',
      role: 'owner',
      joinedAt: new Date('2024-01-15'),
      status: 'active'
    }
  ];

  async getOrganizations(userId: string = 'user-1'): Promise<Organization[]> {
    await new Promise(resolve => setTimeout(resolve, 100));
    // Filter organizations where user is a member
    const userOrgIds = this.members
      .filter(m => m.userId === userId && m.status === 'active')
      .map(m => m.organizationId);
    
    return this.organizations.filter(org => userOrgIds.includes(org.id));
  }

  async getOrganization(id: string): Promise<Organization | null> {
    await new Promise(resolve => setTimeout(resolve, 100));
    return this.organizations.find(org => org.id === id) || null;
  }

  async createOrganization(request: CreateOrganizationRequest): Promise<Organization> {
    await new Promise(resolve => setTimeout(resolve, 200));
    
    const newOrg: Organization = {
      id: Date.now().toString(),
      name: request.name,
      slug: request.slug,
      description: request.description,
      createdAt: new Date(),
      updatedAt: new Date(),
      ownerId: 'user-1', // In real app, get from auth context
      settings: {
        maxStorageGB: 10,
        maxUsers: 5,
        features: ['basic-features']
      }
    };

    this.organizations.push(newOrg);
    
    // Add user as owner
    this.members.push({
      id: Date.now().toString(),
      organizationId: newOrg.id,
      userId: 'user-1',
      role: 'owner',
      joinedAt: new Date(),
      status: 'active'
    });

    return newOrg;
  }

  async getProjects(organizationId: string): Promise<Project[]> {
    await new Promise(resolve => setTimeout(resolve, 100));
    return this.projects.filter(p => p.organizationId === organizationId && p.status === 'active');
  }

  async getProject(id: string): Promise<Project | null> {
    await new Promise(resolve => setTimeout(resolve, 100));
    return this.projects.find(p => p.id === id) || null;
  }

  async createProject(request: CreateProjectRequest): Promise<Project> {
    await new Promise(resolve => setTimeout(resolve, 200));
    
    const newProject: Project = {
      id: Date.now().toString(),
      name: request.name,
      slug: request.slug,
      description: request.description,
      organizationId: request.organizationId,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdById: 'user-1',
      status: 'active',
      settings: {}
    };

    this.projects.push(newProject);
    return newProject;
  }

  async getUserRole(organizationId: string, userId: string = 'user-1'): Promise<OrganizationMember['role'] | null> {
    await new Promise(resolve => setTimeout(resolve, 50));
    const member = this.members.find(m => 
      m.organizationId === organizationId && 
      m.userId === userId && 
      m.status === 'active'
    );
    return member?.role || null;
  }

  async getOrganizationContext(
    organizationId?: string, 
    projectId?: string,
    userId: string = 'user-1'
  ): Promise<OrganizationContext> {
    const availableOrganizations = await this.getOrganizations(userId);
    
    let organization: Organization | null = null;
    let project: Project | null = null;
    let userRole: OrganizationMember['role'] | null = null;
    let availableProjects: Project[] = [];

    if (organizationId) {
      organization = await this.getOrganization(organizationId);
      if (organization) {
        userRole = await this.getUserRole(organizationId, userId);
        availableProjects = await this.getProjects(organizationId);
        
        if (projectId) {
          project = await this.getProject(projectId);
        }
      }
    }

    return {
      organization,
      project,
      userRole,
      availableOrganizations,
      availableProjects
    };
  }
}