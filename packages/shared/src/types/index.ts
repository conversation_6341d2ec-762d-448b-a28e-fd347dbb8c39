export interface Document {
  id: string;
  name: string;
  type: 'PDF' | 'Markdown' | 'DOCX' | 'TXT' | 'CSV' | 'XLSX' | 'PPTX' | 'IMAGE';
  size: number;
  uploadDate: Date;
  category: string;
  tags: string[];
  lastModified: Date;
  url?: string;
  organizationId: string;
  projectId?: string;
  uploadedBy: string;
}

export interface DocumentFilter {
  searchTerm: string;
  type: string;
  category: string;
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  sortBy: 'name' | 'date' | 'size' | 'type';
  sortOrder: 'asc' | 'desc';
}

export interface UploadProgress {
  id: string;
  name: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
}

export interface CreateDocumentRequest {
  name: string;
  type: Document['type'];
  size: number;
  category: string;
  tags: string[];
  organizationId: string;
  projectId?: string;
}

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// Re-export organization types
export * from './organization.js';