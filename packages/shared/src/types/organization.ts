export interface Organization {
  id: string;
  name: string;
  slug: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  ownerId: string;
  settings: {
    maxStorageGB: number;
    maxUsers: number;
    features: string[];
  };
}

export interface Project {
  id: string;
  name: string;
  slug: string;
  description?: string;
  organizationId: string;
  createdAt: Date;
  updatedAt: Date;
  createdById: string;
  status: 'active' | 'archived' | 'deleted';
  settings: {
    documentRetentionDays?: number;
    allowedFileTypes?: string[];
  };
}

export interface OrganizationMember {
  id: string;
  organizationId: string;
  userId: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  joinedAt: Date;
  invitedBy?: string;
  status: 'active' | 'invited' | 'suspended';
}

export interface CreateOrganizationRequest {
  name: string;
  slug: string;
  description?: string;
}

export interface CreateProjectRequest {
  name: string;
  slug: string;
  description?: string;
  organizationId: string;
}

export interface OrganizationContext {
  organization: Organization | null;
  project: Project | null;
  userRole: OrganizationMember['role'] | null;
  availableOrganizations: Organization[];
  availableProjects: Project[];
}