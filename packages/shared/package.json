{"name": "@docmanager/shared", "version": "0.0.0", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch --preserveWatchOutput", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "test": "vitest", "clean": "rm -rf dist .turbo"}, "devDependencies": {"@docmanager/config": "*", "@typescript-eslint/parser": "^8.18.1", "eslint": "^9.17.0", "typescript": "^5.7.2", "vitest": "^3.2.4"}}