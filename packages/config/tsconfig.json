{"compilerOptions": {"target": "ES2022", "lib": ["ES2023"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*", "*.js"], "exclude": ["node_modules", "dist"]}