{"name": "@docmanager/config", "version": "0.0.0", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./eslint": "./eslint.js", "./tailwind": "./tailwind.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist .turbo"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^9.17.0", "typescript": "^5.7.2"}}