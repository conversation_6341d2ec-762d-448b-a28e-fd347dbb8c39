# Document Management Monorepo

A modern, scalable document management system built with TypeScript, React, and Express.js using Turborepo for monorepo management.

## 🏗️ Architecture

This monorepo contains:

### Applications
- **`apps/web`** - React frontend application with Vite
- **`apps/api`** - Express.js backend API

### Packages
- **`packages/shared`** - Shared types, utilities, and business logic
- **`packages/ui`** - Reusable UI components library
- **`packages/config`** - Shared configuration (ESLint, Tailwind, etc.)

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm 8+

### Installation

```bash
# Install dependencies for all packages
npm install

# Install Turborepo globally (optional)
npm install -g turbo
```

### Development

```bash
# Start all applications in development mode
npm run dev

# Start only the web app
npm run dev --filter=@docmanager/web

# Start only the API
npm run dev --filter=@docmanager/api
```

### Building

```bash
# Build all packages and applications
npm run build

# Build specific package
npm run build --filter=@docmanager/shared
```

### Testing & Linting

```bash
# Run tests across all packages
npm run test

# Run linting
npm run lint

# Type checking
npm run type-check

# Format code
npm run format
```

## 📁 Project Structure

```
├── apps/
│   ├── web/                 # React frontend
│   │   ├── src/
│   │   ├── public/
│   │   ├── package.json
│   │   └── vite.config.ts
│   └── api/                 # Express.js backend
│       ├── src/
│       ├── package.json
│       └── tsconfig.json
├── packages/
│   ├── shared/              # Shared business logic
│   │   ├── src/
│   │   │   ├── types/
│   │   │   ├── services/
│   │   │   └── utils/
│   │   └── package.json
│   ├── ui/                  # UI components library
│   │   ├── src/components/
│   │   └── package.json
│   └── config/              # Shared configurations
│       ├── eslint.js
│       ├── tailwind.js
│       └── package.json
├── turbo.json              # Turborepo configuration
├── package.json            # Root package.json
└── README.md
```

## 🔧 Development Workflow

### Adding New Packages

1. Create new directory in `packages/`
2. Add `package.json` with proper naming convention (`@docmanager/package-name`)
3. Update root `package.json` workspaces if needed
4. Add build pipeline to `turbo.json`

### Adding New Applications

1. Create new directory in `apps/`
2. Follow same package naming convention
3. Configure build and dev scripts
4. Update Turborepo pipeline

### Package Dependencies

Use workspace protocol for internal dependencies:

```json
{
  "dependencies": {
    "@docmanager/shared": "workspace:*"
  }
}
```

## 🏃‍♂️ Scripts Reference

| Command | Description |
|---------|-------------|
| `npm run dev` | Start all apps in development |
| `npm run build` | Build all packages and apps |
| `npm run lint` | Lint all packages |
| `npm run test` | Run tests across monorepo |
| `npm run type-check` | TypeScript type checking |
| `npm run clean` | Clean all build artifacts |
| `npm run format` | Format code with Prettier |

## 🔄 Migration from Single Project

The migration process involved:

1. **Restructuring**: Moved existing code to `apps/web/`
2. **Extracting Shared Code**: Created `packages/shared/` for common logic
3. **Setting up Turborepo**: Added `turbo.json` and updated scripts
4. **Creating API**: Built new Express.js API in `apps/api/`
5. **Package Dependencies**: Updated imports to use workspace packages

## 🚀 Deployment

### Web Application
```bash
# Build for production
npm run build --filter=@docmanager/web

# Preview production build
npm run preview --filter=@docmanager/web
```

### API Application
```bash
# Build API
npm run build --filter=@docmanager/api

# Start production server
npm run start --filter=@docmanager/api
```

## 🤝 Contributing

1. Create feature branch from `main`
2. Make changes in appropriate packages
3. Run `npm run lint` and `npm run test`
4. Submit pull request

## 📦 Package Management

This monorepo uses npm workspaces with Turborepo for:
- **Dependency Management**: Shared dependencies hoisted to root
- **Build Orchestration**: Parallel builds with dependency awareness
- **Caching**: Intelligent build and test caching
- **Task Pipeline**: Optimized task execution order

## 🔮 Future Enhancements

- [ ] Add more shared packages (analytics, auth, etc.)
- [ ] Implement proper CI/CD pipeline
- [ ] Add E2E testing setup
- [ ] Create mobile application
- [ ] Add microservice applications
- [ ] Implement design system package