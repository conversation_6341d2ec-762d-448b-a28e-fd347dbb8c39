{"name": "document-management-monorepo", "private": true, "version": "0.0.0", "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "predev": "npm run build", "dev": "concurrently \"npm run dev --workspace=@docmanager/api\" \"npm run dev --workspace=@docmanager/web\"", "dev:api": "npm run build && npm run dev --workspace=@docmanager/api", "dev:web": "npm run dev --workspace=@docmanager/web", "lint": "turbo run lint", "type-check": "turbo run type-check", "test": "turbo run test", "clean": "turbo run clean", "turbo:build": "turbo run build", "turbo:dev": "turbo run dev", "turbo:dev:api": "turbo run dev --filter=@docmanager/api", "turbo:dev:web": "turbo run dev --filter=@docmanager/web", "turbo:lint": "turbo run lint", "turbo:type-check": "turbo run type-check", "turbo:test": "turbo run test", "turbo:clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo run build lint test && changeset publish"}, "engines": {"node": ">=18.0.0", "npm": ">=10.0.0"}, "packageManager": "npm@10.9.0", "dependencies": {"lucide-react": "^0.468.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@changesets/cli": "^2.27.9", "@eslint/js": "^9.17.0", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "prettier": "^3.3.3", "tailwindcss": "^3.4.17", "turbo": "^2.3.0", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "vite": "^6.0.5"}}